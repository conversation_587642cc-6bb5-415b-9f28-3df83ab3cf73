<!DOCTYPE html>
<html lang="{{ locale() }}" dir="{{ currentLocale() === 'ar' ? 'rtl' : 'ltr' }}" class="scroll-smooth">
<x-head :noIndex="$noIndex ?? false" />

<body class="flex flex-col min-h-screen antialiased bg-bg-color text-text-color transition-colors duration-300">
    {{-- Skip to main content for accessibility --}}
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-md z-50">
        {{ __('Skip to main content') }}
    </a>

    <x-no-script />
    <x-navbar />

    @if ($sidebarSettings->enableSidebar)
        <div class="container grid grid-rows-[auto_1fr] lg:grid-rows-[auto] min-h-screen gap-8 lg:grid-cols-3 mb-8 flex-1">
            <main id="main-content" class="flex flex-col lg:col-span-2" role="main">
                {{ $slot }}
            </main>
            <aside class="lg:order-last" role="complementary" aria-label="{{ __('Sidebar') }}">
                <x-sidebar />
            </aside>
        </div>
    @else
        <div class="container flex-1 mb-8">
            <main id="main-content" class="flex flex-col" role="main">
                {{ $slot }}
            </main>
        </div>
    @endif

    <x-footer />
    <x-to-up />
    <livewire:alert-offline />
    @livewire('notifications')
    @livewireScriptConfig
    @include('cookie-consent::index')

    {{-- Performance monitoring script --}}
    <script>
        // Monitor Core Web Vitals
        if ('web-vital' in window) {
            import('https://unpkg.com/web-vitals@3/dist/web-vitals.js').then(({getCLS, getFID, getFCP, getLCP, getTTFB}) => {
                getCLS(console.log);
                getFID(console.log);
                getFCP(console.log);
                getLCP(console.log);
                getTTFB(console.log);
            });
        }
    </script>
</body>

</html>
