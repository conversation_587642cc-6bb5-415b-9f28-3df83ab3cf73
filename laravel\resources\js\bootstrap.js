/**
 * Bootstrap file for Image Converter Application
 * Sets up essential configurations and polyfills
 */

// Import Axios for HTTP requests
import axios from 'axios';

// Set up Axios defaults
window.axios = axios;
window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

// CSRF Token setup
const token = document.head.querySelector('meta[name="csrf-token"]');
if (token) {
    window.axios.defaults.headers.common['X-CSRF-TOKEN'] = token.content;
} else {
    console.error('CSRF token not found: https://laravel.com/docs/csrf#csrf-x-csrf-token');
}

// Lodash utilities
import _ from 'lodash';
window._ = _;

// Echo for real-time events (if needed)
// import Echo from 'laravel-echo';
// import Pusher from 'pusher-js';
// window.Pusher = Pusher;
// window.Echo = new Echo({
//     broadcaster: 'pusher',
//     key: process.env.MIX_PUSHER_APP_KEY,
//     cluster: process.env.MIX_PUSHER_APP_CLUSTER,
//     forceTLS: true
// });

// Performance monitoring
if ('performance' in window && 'PerformanceObserver' in window) {
    // Monitor navigation timing
    window.addEventListener('load', () => {
        const navigation = performance.getEntriesByType('navigation')[0];
        if (navigation) {
            console.log('Page load time:', navigation.loadEventEnd - navigation.fetchStart, 'ms');
        }
    });
}

// Error handling
window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
    
    // Send to error tracking service if available
    if (typeof gtag !== 'undefined') {
        gtag('event', 'exception', {
            description: event.error.message,
            fatal: false
        });
    }
});

// Unhandled promise rejection handling
window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    
    // Send to error tracking service if available
    if (typeof gtag !== 'undefined') {
        gtag('event', 'exception', {
            description: event.reason.message || 'Unhandled promise rejection',
            fatal: false
        });
    }
});

// Polyfills for older browsers
if (!window.requestIdleCallback) {
    window.requestIdleCallback = function(cb) {
        const start = Date.now();
        return setTimeout(() => {
            cb({
                didTimeout: false,
                timeRemaining() {
                    return Math.max(0, 50 - (Date.now() - start));
                }
            });
        }, 1);
    };
}

if (!window.cancelIdleCallback) {
    window.cancelIdleCallback = function(id) {
        clearTimeout(id);
    };
}

// Intersection Observer polyfill check
if (!('IntersectionObserver' in window)) {
    import('intersection-observer').then(() => {
        console.log('IntersectionObserver polyfill loaded');
    });
}

// Web Vitals monitoring
if ('web-vitals' in window || typeof webVitals !== 'undefined') {
    // Will be handled by performance-enhancements.js
} else {
    // Fallback basic performance monitoring
    window.addEventListener('load', () => {
        if ('performance' in window) {
            const timing = performance.timing;
            const loadTime = timing.loadEventEnd - timing.navigationStart;
            console.log('Page load time (fallback):', loadTime, 'ms');
        }
    });
}

// Accessibility enhancements
document.addEventListener('DOMContentLoaded', () => {
    // Add focus-visible polyfill behavior
    if (!CSS.supports('selector(:focus-visible)')) {
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });
        
        document.addEventListener('mousedown', () => {
            document.body.classList.remove('keyboard-navigation');
        });
    }
    
    // Enhance form accessibility
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            if (!input.getAttribute('aria-label') && !input.getAttribute('aria-labelledby')) {
                const label = form.querySelector(`label[for="${input.id}"]`);
                if (label) {
                    input.setAttribute('aria-labelledby', label.id || `label-${input.id}`);
                    if (!label.id) {
                        label.id = `label-${input.id}`;
                    }
                }
            }
        });
    });
});

// RTL support detection and setup
const isRTL = document.documentElement.dir === 'rtl' || 
              document.documentElement.getAttribute('lang') === 'ar';

if (isRTL) {
    document.body.classList.add('rtl-layout');
    
    // Adjust scroll behavior for RTL
    const originalScrollTo = window.scrollTo;
    window.scrollTo = function(x, y) {
        if (typeof x === 'object') {
            return originalScrollTo.call(this, x);
        }
        return originalScrollTo.call(this, x, y);
    };
}

// Theme detection and setup
const prefersDark = window.matchMedia('(prefers-color-scheme: dark)');
const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');

// Apply theme preferences
if (prefersDark.matches) {
    document.body.classList.add('dark-theme');
}

if (prefersReducedMotion.matches) {
    document.body.classList.add('reduced-motion');
}

// Listen for theme changes
prefersDark.addEventListener('change', (e) => {
    if (e.matches) {
        document.body.classList.add('dark-theme');
    } else {
        document.body.classList.remove('dark-theme');
    }
});

prefersReducedMotion.addEventListener('change', (e) => {
    if (e.matches) {
        document.body.classList.add('reduced-motion');
    } else {
        document.body.classList.remove('reduced-motion');
    }
});

// Connection status monitoring
if ('navigator' in window && 'onLine' in navigator) {
    const updateConnectionStatus = () => {
        if (navigator.onLine) {
            document.body.classList.remove('offline');
            document.body.classList.add('online');
        } else {
            document.body.classList.remove('online');
            document.body.classList.add('offline');
        }
    };
    
    window.addEventListener('online', updateConnectionStatus);
    window.addEventListener('offline', updateConnectionStatus);
    updateConnectionStatus(); // Initial check
}

// Memory usage monitoring (for development)
if (process.env.NODE_ENV === 'development' && 'memory' in performance) {
    setInterval(() => {
        const memory = performance.memory;
        if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
            console.warn('High memory usage detected:', {
                used: Math.round(memory.usedJSHeapSize / 1048576) + ' MB',
                limit: Math.round(memory.jsHeapSizeLimit / 1048576) + ' MB'
            });
        }
    }, 30000); // Check every 30 seconds
}

// Export utilities for global use
window.ImageConverterUtils = {
    isRTL,
    prefersDark: prefersDark.matches,
    prefersReducedMotion: prefersReducedMotion.matches,
    isOnline: navigator.onLine,
    
    // Utility functions
    debounce: _.debounce,
    throttle: _.throttle,
    
    // Format file size
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    // Copy to clipboard
    async copyToClipboard(text) {
        if (navigator.clipboard) {
            try {
                await navigator.clipboard.writeText(text);
                return true;
            } catch (err) {
                console.error('Failed to copy to clipboard:', err);
                return false;
            }
        }
        return false;
    },
    
    // Show toast notification
    showToast(message, type = 'info', duration = 5000) {
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300 ${
            type === 'error' ? 'bg-red-500 text-white' :
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'warning' ? 'bg-yellow-500 text-black' :
            'bg-blue-500 text-white'
        }`;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.classList.remove('translate-x-full');
        }, 100);
        
        setTimeout(() => {
            toast.classList.add('translate-x-full');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
    }
};
