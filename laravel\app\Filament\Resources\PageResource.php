<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PageResource\Pages;
use App\Models\Page;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Mohamedsabil83\FilamentFormsTinyeditor\Components\TinyEditor;

class PageResource extends Resource
{
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = 'Custom Pages';
    protected static ?string $navigationGroup = 'Content';
    protected static ?int $navigationSort = 3;

    protected static ?string $model = Page::class;

    public static array $toSanitize = [
        'title',
        'slug',
        'label',
        'metaDescription',
        'metaKeywords'
    ];

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Language and Basic Info Section
                Section::make('📄 Page Information')
                    ->description('Set the language and basic details for your page')
                    ->schema([
                        Grid::make(3)->schema([
                            Select::make('locale')
                                ->label('🌍 Language')
                                ->options([
                                    'en' => '🇺🇸 English',
                                    'ar' => '🇸🇦 العربية (Arabic)',
                                ])
                                ->default('en')
                                ->required()
                                ->reactive()
                                ->helperText('Choose the language for this page'),

                            DatePicker::make('published_at')
                                ->label('📅 Published Date')
                                ->default(now())
                                ->helperText('When should this page be published?'),

                            Select::make('location')
                                ->label('📍 Display Location')
                                ->required()
                                ->options([
                                    'navbar' => '🔝 Navigation Bar',
                                    'footer' => '🦶 Footer',
                                    'both' => '🔄 Both',
                                    'none' => '🚫 Hidden',
                                ])
                                ->default('none')
                                ->helperText('Where should this page appear?'),
                        ]),

                        Grid::make(2)->schema([
                            TextInput::make('title')
                                ->label('📰 Page Title')
                                ->reactive()
                                ->placeholder('Enter a clear title for your page')
                                ->afterStateUpdated(function ($state, $set, $get) {
                                    $locale = $get('locale') ?? 'en';
                                    if ($locale === 'ar') {
                                        // For Arabic, keep original characters
                                        $set('slug', $state);
                                    } else {
                                        $set('slug', Str::slug($state));
                                    }
                                })
                                ->required()
                                ->maxLength(100)
                                ->helperText('Max 100 characters - This will be the main heading'),

                            TextInput::make('slug')
                                ->label('🔗 URL Slug')
                                ->placeholder('page-url-slug')
                                ->required()
                                ->maxLength(100)
                                ->helperText('URL-friendly version of the title')
                                ->unique(Page::class, 'slug', ignoreRecord: true),
                        ]),

                        TextInput::make('label')
                            ->label('🏷️ Menu Label')
                            ->placeholder('Text shown in navigation')
                            ->helperText('Link text that shows in navbar or footer')
                            ->required()
                            ->maxLength(50),
                    ]),

                // SEO Section
                Section::make('🔍 SEO Optimization')
                    ->description('Optimize your page for search engines')
                    ->schema([
                        Textarea::make('metaDescription')
                            ->label('📝 Meta Description')
                            ->placeholder('Brief description for search engines')
                            ->rows(3)
                            ->maxLength(160)
                            ->helperText('Max 160 characters - This appears in search results')
                            ->required(),

                        Textarea::make('metaKeywords')
                            ->label('🔑 Meta Keywords')
                            ->placeholder('keyword1, keyword2, keyword3')
                            ->helperText('Comma-separated keywords (optional)')
                            ->rows(2),

                        Toggle::make('noIndex')
                            ->label('🚫 No Index')
                            ->helperText('Prevent search engines from indexing this page')
                            ->default(false),
                    ]),

                // Content Section
                Section::make('📝 Content')
                    ->description('Write your page content')
                    ->schema([
                        TinyEditor::make('content')
                            ->label('📄 Page Content')
                            ->profile('custom')
                            ->fileAttachmentsDisk('public_storage')
                            ->required()
                            ->helperText('Write your full page content here'),
                    ]),

                // Display Settings Section
                Section::make('⚙️ Display Settings')
                    ->description('Control how this page appears on your site')
                    ->schema([
                        Grid::make(3)->schema([
                            Toggle::make('topAd')
                                ->label('📢 Show Top Ad')
                                ->default(false)
                                ->helperText('Display advertisement at the top'),

                            Toggle::make('bottomAd')
                                ->label('📢 Show Bottom Ad')
                                ->default(false)
                                ->helperText('Display advertisement at the bottom'),

                            Toggle::make('showShareButtons')
                                ->label('📤 Show Share Buttons')
                                ->default(false)
                                ->helperText('Enable social media sharing'),
                        ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('title')
                    ->label('📄 Title')
                    ->searchable()
                    ->sortable()
                    ->limit(50)
                    ->tooltip(fn ($record) => $record->title),

                TextColumn::make('locale')
                    ->label('🌍 Language')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'en' => '🇺🇸 English',
                        'ar' => '🇸🇦 العربية',
                        default => $state,
                    })
                    ->colors([
                        'primary' => 'en',
                        'success' => 'ar',
                    ])
                    ->sortable(),

                TextColumn::make('slug')
                    ->label('🔗 URL')
                    ->limit(30)
                    ->copyable()
                    ->copyMessage('URL copied!')
                    ->tooltip(fn ($record) => $record->slug),

                TextColumn::make('status')
                    ->label('📊 Status')
                    ->badge()
                    ->getStateUsing(function ($record): string {
                        if (!$record->published_at) return 'Draft';
                        return $record->published_at->isPast() ? 'Published' : 'Scheduled';
                    })
                    ->colors([
                        'success' => 'Published',
                        'warning' => 'Scheduled',
                        'gray' => 'Draft',
                    ]),

                TextColumn::make('location')
                    ->label('📍 Location')
                    ->badge()
                    ->formatStateUsing(fn ($state) => match($state) {
                        'both' => '🔄 Both',
                        'navbar' => '🔝 Navbar',
                        'footer' => '🦶 Footer',
                        'none' => '🚫 Hidden',
                        default => $state,
                    })
                    ->colors([
                        'success' => 'both',
                        'primary' => 'navbar',
                        'warning' => 'footer',
                        'gray' => 'none',
                    ]),

                TextColumn::make('published_at')
                    ->label('📅 Published')
                    ->sortable()
                    ->date('M j, Y')
                    ->placeholder('Not published'),

                TextColumn::make('views_count')
                    ->label('👁️ Views')
                    ->getStateUsing(fn ($record) => number_format(views($record)->count()))
                    ->sortable(),
            ])
            ->filters([
                \Filament\Tables\Filters\SelectFilter::make('locale')
                    ->label('Language')
                    ->options([
                        'en' => '🇺🇸 English',
                        'ar' => '🇸🇦 العربية',
                    ]),

                \Filament\Tables\Filters\SelectFilter::make('location')
                    ->label('Display Location')
                    ->options([
                        'navbar' => 'Navigation Bar',
                        'footer' => 'Footer',
                        'both' => 'Both',
                        'none' => 'Hidden',
                    ]),

                \Filament\Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'published' => 'Published',
                        'draft' => 'Draft',
                        'scheduled' => 'Scheduled',
                    ])
                    ->query(function ($query, $data) {
                        return match ($data['value'] ?? null) {
                            'published' => $query->whereNotNull('published_at')->where('published_at', '<=', now()),
                            'draft' => $query->whereNull('published_at'),
                            'scheduled' => $query->whereNotNull('published_at')->where('published_at', '>', now()),
                            default => $query,
                        };
                    }),
            ])
            ->actions([
                \Filament\Tables\Actions\ViewAction::make()
                    ->url(fn ($record) => url($record->slug))
                    ->openUrlInNewTab(),

                EditAction::make(),

                DeleteAction::make()->after(
                    function ($record) {
                        cache()->flush();
                    }
                ),
            ])
            ->bulkActions([
                \Filament\Tables\Actions\BulkActionGroup::make([
                    \Filament\Tables\Actions\DeleteBulkAction::make(),

                    \Filament\Tables\Actions\BulkAction::make('publish')
                        ->label('Publish Selected')
                        ->icon('heroicon-o-check')
                        ->action(function ($records) {
                            $records->each(function ($record) {
                                $record->update(['published_at' => now()]);
                            });
                        })
                        ->requiresConfirmation()
                        ->color('success'),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPages::route('/'),
            'create' => Pages\CreatePage::route('/create'),
            'edit' => Pages\EditPage::route('/{record}/edit'),
        ];
    }
}
