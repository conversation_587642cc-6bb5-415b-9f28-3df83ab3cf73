<?php

namespace Database\Seeders;

use App\Models\Post;
use App\Models\Page;
use Illuminate\Database\Seeder;

class MultilingualContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->createEnglishPosts();
        $this->createArabicPosts();
        $this->createEnglishPages();
        $this->createArabicPages();
    }

    private function createEnglishPosts(): void
    {
        $englishPosts = [
            [
                'title' => 'Best Image Formats for Web Development',
                'slug' => 'best-image-formats-web-development',
                'content' => '<h2>Introduction to Web Image Formats</h2><p>Choosing the right image format is crucial for web performance and user experience. In this comprehensive guide, we\'ll explore the most popular image formats and their best use cases.</p><h3>JPEG - The Universal Choice</h3><p>JPEG is perfect for photographs and complex images with many colors. It offers excellent compression while maintaining good quality.</p><h3>PNG - For Transparency and Graphics</h3><p>PNG is ideal for logos, icons, and images that require transparency. It provides lossless compression for graphics.</p><h3>WebP - The Modern Solution</h3><p>WebP offers superior compression compared to JPEG and PNG while supporting both lossy and lossless compression.</p>',
                'metaDescription' => 'Learn about the best image formats for web development including JPEG, PNG, WebP, and more. Complete guide with examples and best practices.',
                'metaKeywords' => 'image formats, web development, JPEG, PNG, WebP, optimization',
                'snippet' => 'Discover the best image formats for web development and learn when to use JPEG, PNG, WebP, and other formats for optimal performance.',
                'locale' => 'en',
                'featured' => true,
                'tags' => ['web development', 'image optimization', 'performance'],
                'author_name' => 'Web Development Team',
                'reading_time' => 5,
            ],
            [
                'title' => 'How to Convert Images to WebP Format',
                'slug' => 'convert-images-webp-format',
                'content' => '<h2>Why Convert to WebP?</h2><p>WebP is a modern image format that provides superior compression for images on the web. Converting your images to WebP can significantly reduce file sizes and improve website loading times.</p><h3>Benefits of WebP</h3><ul><li>25-35% smaller file sizes compared to JPEG</li><li>Support for both lossy and lossless compression</li><li>Transparency support like PNG</li><li>Animation support like GIF</li></ul><h3>How to Convert</h3><p>You can convert images to WebP using our online converter tool. Simply upload your images and download the converted WebP files.</p>',
                'metaDescription' => 'Learn how to convert images to WebP format for better web performance. Step-by-step guide with tools and best practices.',
                'metaKeywords' => 'WebP conversion, image optimization, web performance, file compression',
                'snippet' => 'Step-by-step guide on converting images to WebP format for improved web performance and faster loading times.',
                'locale' => 'en',
                'featured' => false,
                'tags' => ['WebP', 'image conversion', 'optimization'],
                'author_name' => 'Technical Team',
                'reading_time' => 3,
            ],
            [
                'title' => 'Image Optimization Tips for Better SEO',
                'slug' => 'image-optimization-tips-seo',
                'content' => '<h2>Why Image Optimization Matters for SEO</h2><p>Optimized images can significantly improve your website\'s search engine rankings and user experience. Here are the essential tips for image SEO.</p><h3>1. Choose the Right File Format</h3><p>Select appropriate formats: JPEG for photos, PNG for graphics with transparency, WebP for modern browsers.</p><h3>2. Optimize File Sizes</h3><p>Compress images without losing quality. Aim for the smallest file size that maintains visual quality.</p><h3>3. Use Descriptive File Names</h3><p>Name your files descriptively instead of using generic names like "image1.jpg".</p><h3>4. Add Alt Text</h3><p>Always include descriptive alt text for accessibility and SEO benefits.</p>',
                'metaDescription' => 'Essential image optimization tips for better SEO rankings. Learn about file formats, compression, alt text, and more.',
                'metaKeywords' => 'image SEO, optimization, alt text, file formats, web performance',
                'snippet' => 'Boost your SEO rankings with these essential image optimization tips including file formats, compression, and alt text best practices.',
                'locale' => 'en',
                'featured' => true,
                'tags' => ['SEO', 'image optimization', 'web performance'],
                'author_name' => 'SEO Team',
                'reading_time' => 4,
            ],
        ];

        foreach ($englishPosts as $postData) {
            Post::create(array_merge($postData, [
                'published_at' => now()->subDays(rand(1, 30)),
            ]));
        }
    }

    private function createArabicPosts(): void
    {
        $arabicPosts = [
            [
                'title' => 'أفضل تنسيقات الصور لتطوير الويب',
                'slug' => 'أفضل-تنسيقات-الصور-لتطوير-الويب',
                'content' => '<h2>مقدمة حول تنسيقات الصور للويب</h2><p>اختيار تنسيق الصورة المناسب أمر بالغ الأهمية لأداء الويب وتجربة المستخدم. في هذا الدليل الشامل، سنستكشف تنسيقات الصور الأكثر شيوعاً وأفضل حالات استخدامها.</p><h3>JPEG - الخيار العالمي</h3><p>JPEG مثالي للصور الفوتوغرافية والصور المعقدة ذات الألوان المتعددة. يوفر ضغطاً ممتازاً مع الحفاظ على جودة جيدة.</p><h3>PNG - للشفافية والرسوميات</h3><p>PNG مثالي للشعارات والأيقونات والصور التي تتطلب الشفافية. يوفر ضغطاً بدون فقدان للرسوميات.</p><h3>WebP - الحل الحديث</h3><p>WebP يوفر ضغطاً فائقاً مقارنة بـ JPEG و PNG مع دعم الضغط مع وبدون فقدان.</p>',
                'metaDescription' => 'تعرف على أفضل تنسيقات الصور لتطوير الويب بما في ذلك JPEG و PNG و WebP والمزيد. دليل شامل مع أمثلة وأفضل الممارسات.',
                'metaKeywords' => 'تنسيقات الصور، تطوير الويب، JPEG، PNG، WebP، تحسين',
                'snippet' => 'اكتشف أفضل تنسيقات الصور لتطوير الويب وتعلم متى تستخدم JPEG و PNG و WebP وتنسيقات أخرى للحصول على أداء مثالي.',
                'locale' => 'ar',
                'featured' => true,
                'tags' => ['تطوير الويب', 'تحسين الصور', 'الأداء'],
                'author_name' => 'فريق تطوير الويب',
                'reading_time' => 5,
            ],
            [
                'title' => 'كيفية تحويل الصور إلى تنسيق WebP',
                'slug' => 'كيفية-تحويل-الصور-إلى-تنسيق-webp',
                'content' => '<h2>لماذا التحويل إلى WebP؟</h2><p>WebP هو تنسيق صور حديث يوفر ضغطاً فائقاً للصور على الويب. تحويل صورك إلى WebP يمكن أن يقلل بشكل كبير من أحجام الملفات ويحسن أوقات تحميل الموقع.</p><h3>فوائد WebP</h3><ul><li>أحجام ملفات أصغر بنسبة 25-35% مقارنة بـ JPEG</li><li>دعم الضغط مع وبدون فقدان</li><li>دعم الشفافية مثل PNG</li><li>دعم الرسوم المتحركة مثل GIF</li></ul><h3>كيفية التحويل</h3><p>يمكنك تحويل الصور إلى WebP باستخدام أداة المحول الخاصة بنا. ما عليك سوى رفع صورك وتنزيل ملفات WebP المحولة.</p>',
                'metaDescription' => 'تعلم كيفية تحويل الصور إلى تنسيق WebP لتحسين أداء الويب. دليل خطوة بخطوة مع الأدوات وأفضل الممارسات.',
                'metaKeywords' => 'تحويل WebP، تحسين الصور، أداء الويب، ضغط الملفات',
                'snippet' => 'دليل خطوة بخطوة حول تحويل الصور إلى تنسيق WebP لتحسين أداء الويب وأوقات التحميل الأسرع.',
                'locale' => 'ar',
                'featured' => false,
                'tags' => ['WebP', 'تحويل الصور', 'التحسين'],
                'author_name' => 'الفريق التقني',
                'reading_time' => 3,
            ],
            [
                'title' => 'نصائح تحسين الصور لتحسين محركات البحث',
                'slug' => 'نصائح-تحسين-الصور-لتحسين-محركات-البحث',
                'content' => '<h2>لماذا يهم تحسين الصور لتحسين محركات البحث</h2><p>الصور المحسنة يمكن أن تحسن بشكل كبير من ترتيب موقعك في محركات البحث وتجربة المستخدم. إليك النصائح الأساسية لتحسين الصور لمحركات البحث.</p><h3>1. اختر تنسيق الملف المناسب</h3><p>اختر التنسيقات المناسبة: JPEG للصور، PNG للرسوميات مع الشفافية، WebP للمتصفحات الحديثة.</p><h3>2. حسن أحجام الملفات</h3><p>اضغط الصور دون فقدان الجودة. اهدف إلى أصغر حجم ملف يحافظ على الجودة البصرية.</p><h3>3. استخدم أسماء ملفات وصفية</h3><p>اسم ملفاتك بشكل وصفي بدلاً من استخدام أسماء عامة مثل "image1.jpg".</p><h3>4. أضف نص بديل</h3><p>أضف دائماً نصاً بديلاً وصفياً لفوائد إمكانية الوصول وتحسين محركات البحث.</p>',
                'metaDescription' => 'نصائح أساسية لتحسين الصور لترتيب أفضل في محركات البحث. تعلم عن تنسيقات الملفات والضغط والنص البديل والمزيد.',
                'metaKeywords' => 'تحسين الصور لمحركات البحث، التحسين، النص البديل، تنسيقات الملفات، أداء الويب',
                'snippet' => 'عزز ترتيبك في محركات البحث مع هذه النصائح الأساسية لتحسين الصور بما في ذلك تنسيقات الملفات والضغط وأفضل ممارسات النص البديل.',
                'locale' => 'ar',
                'featured' => true,
                'tags' => ['تحسين محركات البحث', 'تحسين الصور', 'أداء الويب'],
                'author_name' => 'فريق تحسين محركات البحث',
                'reading_time' => 4,
            ],
        ];

        foreach ($arabicPosts as $postData) {
            Post::create(array_merge($postData, [
                'published_at' => now()->subDays(rand(1, 30)),
            ]));
        }
    }

    private function createEnglishPages(): void
    {
        $englishPages = [
            [
                'title' => 'About Us',
                'slug' => 'about-us',
                'label' => 'About',
                'content' => '<h1>About Image Converter</h1><p>We are dedicated to providing the best online image conversion tools. Our platform supports over 25 image formats and offers fast, secure, and free conversion services.</p><h2>Our Mission</h2><p>To make image conversion accessible to everyone, anywhere, anytime.</p><h2>Why Choose Us?</h2><ul><li>Free and unlimited conversions</li><li>Support for 25+ formats</li><li>Fast processing</li><li>Secure and private</li><li>No registration required</li></ul>',
                'metaDescription' => 'Learn about Image Converter - the best free online tool for converting images to 25+ formats. Fast, secure, and easy to use.',
                'metaKeywords' => 'about us, image converter, online tool, free conversion',
                'location' => 'footer',
                'locale' => 'en',
                'published_at' => now(),
            ],
            [
                'title' => 'Privacy Policy',
                'slug' => 'privacy-policy',
                'label' => 'Privacy',
                'content' => '<h1>Privacy Policy</h1><p>Your privacy is important to us. This policy explains how we collect, use, and protect your information.</p><h2>Information We Collect</h2><p>We only collect information necessary to provide our services.</p><h2>How We Use Information</h2><p>We use information to improve our services and user experience.</p><h2>Data Security</h2><p>We implement appropriate security measures to protect your data.</p>',
                'metaDescription' => 'Read our privacy policy to understand how we protect your data and privacy when using our image conversion services.',
                'metaKeywords' => 'privacy policy, data protection, security',
                'location' => 'footer',
                'locale' => 'en',
                'published_at' => now(),
            ],
        ];

        foreach ($englishPages as $pageData) {
            Page::create($pageData);
        }
    }

    private function createArabicPages(): void
    {
        $arabicPages = [
            [
                'title' => 'من نحن',
                'slug' => 'من-نحن',
                'label' => 'من نحن',
                'content' => '<h1>حول محول الصور</h1><p>نحن ملتزمون بتوفير أفضل أدوات تحويل الصور عبر الإنترنت. منصتنا تدعم أكثر من 25 تنسيق صورة وتوفر خدمات تحويل سريعة وآمنة ومجانية.</p><h2>مهمتنا</h2><p>جعل تحويل الصور متاحاً للجميع، في أي مكان، في أي وقت.</p><h2>لماذا تختارنا؟</h2><ul><li>تحويلات مجانية وغير محدودة</li><li>دعم أكثر من 25 تنسيق</li><li>معالجة سريعة</li><li>آمن وخاص</li><li>لا يتطلب تسجيل</li></ul>',
                'metaDescription' => 'تعرف على محول الصور - أفضل أداة مجانية عبر الإنترنت لتحويل الصور إلى أكثر من 25 تنسيق. سريع وآمن وسهل الاستخدام.',
                'metaKeywords' => 'من نحن، محول الصور، أداة عبر الإنترنت، تحويل مجاني',
                'location' => 'footer',
                'locale' => 'ar',
                'published_at' => now(),
            ],
            [
                'title' => 'سياسة الخصوصية',
                'slug' => 'سياسة-الخصوصية',
                'label' => 'الخصوصية',
                'content' => '<h1>سياسة الخصوصية</h1><p>خصوصيتك مهمة بالنسبة لنا. تشرح هذه السياسة كيف نجمع ونستخدم ونحمي معلوماتك.</p><h2>المعلومات التي نجمعها</h2><p>نجمع فقط المعلومات الضرورية لتقديم خدماتنا.</p><h2>كيف نستخدم المعلومات</h2><p>نستخدم المعلومات لتحسين خدماتنا وتجربة المستخدم.</p><h2>أمان البيانات</h2><p>نطبق تدابير أمنية مناسبة لحماية بياناتك.</p>',
                'metaDescription' => 'اقرأ سياسة الخصوصية الخاصة بنا لفهم كيف نحمي بياناتك وخصوصيتك عند استخدام خدمات تحويل الصور.',
                'metaKeywords' => 'سياسة الخصوصية، حماية البيانات، الأمان',
                'location' => 'footer',
                'locale' => 'ar',
                'published_at' => now(),
            ],
        ];

        foreach ($arabicPages as $pageData) {
            Page::create($pageData);
        }
    }
}
