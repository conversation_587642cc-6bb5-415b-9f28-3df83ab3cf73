<x-layouts.app>
    {{-- JSON-LD Structured Data for WebApplication --}}
    @push('head')
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "{{ $settings->headerTitle }}",
        "description": "{{ $settings->metaDescription ?? $settings->headerSubtitle }}",
        "url": "{{ url('/') }}",
        "applicationCategory": "UtilityApplication",
        "operatingSystem": "Web Browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "featureList": [
            @foreach(converters() as $index => $converter)
                @if($converter['enabled'])
                    "{{ $converter['entryTitle'] }}"{{ $loop->last ? '' : ',' }}
                @endif
            @endforeach
        ]
    }
    </script>
    @endpush

    <div id="home-page" class="space-y-8">
        {{-- <PERSON> <PERSON> with improved accessibility and SEO --}}
        <header class="mb-10 text-center space-y-4" role="banner">
            <h1 class="mb-4 text-3xl md:text-4xl lg:text-5xl font-bold leading-tight text-text-color">
                {{ $settings->headerTitle }}
            </h1>
            <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                {{ $settings->headerSubtitle }}
            </p>

            {{-- Breadcrumb for SEO --}}
            <nav aria-label="{{ __('Breadcrumb') }}" class="hidden">
                <ol itemscope itemtype="https://schema.org/BreadcrumbList">
                    <li itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                        <a itemprop="item" href="{{ url('/') }}">
                            <span itemprop="name">{{ __('Home') }}</span>
                        </a>
                        <meta itemprop="position" content="1" />
                    </li>
                </ol>
            </nav>
        </header>

        @if ($settings->topAd)
            <div class="mb-8 top-ad" role="complementary" aria-label="{{ __('Advertisement') }}">
                {!! $adSettings->topAdCode !!}
            </div>
        @endif
        @php
            $items = collect(converters())
                ->map(fn($converter) => $converter['enabled'])
                ->toArray();
            $showConverters = in_array(true, $items);
            $enabledConverters = collect(converters())->where('enabled', true);
        @endphp

        @if ($showConverters)
            {{-- Converters Section with improved accessibility and SEO --}}
            <section class="mb-8 cards" role="main" aria-labelledby="converters-heading">
                <h2 id="converters-heading" class="sr-only">{{ __('Available Image Converters') }}</h2>

                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 auto-rows-fr">
                    @foreach ($enabledConverters as $converter)
                        <article class="relative group">
                            <a class="block p-6 h-full text-center duration-300 rounded-xl card text-card-text-color bg-card-bg-color shadow-lg hover:shadow-xl hover:scale-105 hover:bg-card-hover-bg-color hover:text-card-hover-text-color transition-all focus:outline-none focus:ring-4 focus:ring-blue-300 focus:ring-opacity-50"
                                href="{{ localizedRoute('resolver', $slugs->{$converter['name']}) }}"
                                aria-label="{{ __('Convert images to :format', ['format' => strtoupper($converter['format'])]) }}"
                                role="link">

                                {{-- View count badge --}}
                                @if ($converter['views'])
                                    <span class="absolute flex items-center text-xs rounded-lg card-views badge badge-ghost top-3 {{ currentLocale() === 'ar' ? 'left-3' : 'right-3' }}"
                                          aria-label="{{ __(':count views', ['count' => $converter['views']]) }}">
                                        <x-icons.eye class="w-4 h-4 {{ currentLocale() === 'ar' ? 'ml-1' : 'mr-1' }}" strokeWidth="2" />
                                        {{ number_format($converter['views']) }}
                                    </span>
                                @endif

                                {{-- Icon container with loading state --}}
                                <div class="flex items-center justify-center w-20 h-20 mx-auto mb-4 rounded-full card-icon bg-zinc-50 group-hover:bg-white transition-colors duration-300"
                                     role="img" aria-label="{{ $converter['format'] }} format icon">
                                    <x-dynamic-component :component="$converter['icon']"
                                                       class="text-card-hover-bg-color w-14 h-14 transition-transform duration-300 group-hover:scale-110" />
                                </div>

                                {{-- Title with proper heading hierarchy --}}
                                <h3 class="block mb-3 text-base font-semibold text-center card-title leading-tight">
                                    {{ $converter['entryTitle'] }}
                                </h3>

                                {{-- Description --}}
                                <p class="text-sm card-subtitle opacity-80 leading-relaxed">
                                    {{ $converter['entrySummary'] }}
                                </p>

                                {{-- Format badge --}}
                                <span class="inline-block mt-3 px-3 py-1 text-xs font-medium uppercase tracking-wide rounded-full bg-blue-100 text-blue-800 group-hover:bg-blue-200 transition-colors duration-300">
                                    {{ $converter['format'] }}
                                </span>
                            </a>
                        </article>
                    @endforeach
                </div>

                {{-- Quick stats for SEO --}}
                <div class="mt-8 text-center">
                    <p class="text-sm text-gray-600">
                        {{ __('Supporting :count image formats with :total total conversions', [
                            'count' => $enabledConverters->count(),
                            'total' => number_format(App\Models\ConvertsCounter::first()->count ?? 0)
                        ]) }}
                    </p>
                </div>
            </section>
            @if ($settings->content)
                @if ($settings->middleAd)
                    <div class="mb-8 middle-ad">
                        {!! $adSettings->middleAdCode !!}
                    </div>
                @endif
                <section class="mb-8 prose about">
                    {!! $settings->content !!}
                </section>
            @endif
            @if ($settings->bottomAd)
                <div class="mb-8 bottom-ad">
                    {!! $adSettings->bottomAdCode !!}
                </div>
            @endif
            @if ($settings->showShareButtons)
                <x-share-buttons />
            @endif
        @else
            <section class="mb-8">
                <div class="container">
                    <h4 class="text-lg font-medium text-center no-converter">
                        {{ __('No Converter') }}
                    </h4>
                </div>
            </section>
        @endif
    </div>
</x-layouts.app>
