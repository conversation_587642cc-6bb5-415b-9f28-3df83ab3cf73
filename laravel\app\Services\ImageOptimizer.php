<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManagerStatic as Image;

/**
 * Image Optimization Service
 * 
 * Provides advanced image optimization features including:
 * - WebP conversion with fallbacks
 * - Responsive image generation
 * - Compression optimization
 * - Format-specific optimizations
 */
class ImageOptimizer
{
    /**
     * Supported image formats for optimization
     */
    private const SUPPORTED_FORMATS = [
        'jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff'
    ];

    /**
     * Default quality settings for different formats
     */
    private const DEFAULT_QUALITY = [
        'jpg' => 85,
        'jpeg' => 85,
        'png' => 9, // PNG compression level (0-9)
        'webp' => 80,
        'gif' => null, // GIF doesn't use quality
        'bmp' => null,
        'tiff' => 85,
    ];

    /**
     * Optimize image for web delivery
     * 
     * @param string $imagePath
     * @param array $options
     * @return array
     */
    public function optimizeForWeb(string $imagePath, array $options = []): array
    {
        try {
            $originalSize = filesize($imagePath);
            $originalFormat = $this->getImageFormat($imagePath);
            
            if (!in_array($originalFormat, self::SUPPORTED_FORMATS)) {
                throw new \InvalidArgumentException("Unsupported image format: {$originalFormat}");
            }

            $optimizedImages = [];
            
            // Generate WebP version if supported
            if ($this->supportsWebP()) {
                $webpPath = $this->convertToWebP($imagePath, $options);
                if ($webpPath) {
                    $optimizedImages['webp'] = [
                        'path' => $webpPath,
                        'size' => filesize($webpPath),
                        'format' => 'webp',
                        'compression_ratio' => $this->calculateCompressionRatio($originalSize, filesize($webpPath))
                    ];
                }
            }

            // Optimize original format
            $optimizedPath = $this->optimizeImage($imagePath, $originalFormat, $options);
            $optimizedImages['original'] = [
                'path' => $optimizedPath,
                'size' => filesize($optimizedPath),
                'format' => $originalFormat,
                'compression_ratio' => $this->calculateCompressionRatio($originalSize, filesize($optimizedPath))
            ];

            // Generate responsive sizes if requested
            if (isset($options['responsive']) && $options['responsive']) {
                $responsiveImages = $this->generateResponsiveSizes($imagePath, $options);
                $optimizedImages['responsive'] = $responsiveImages;
            }

            return [
                'success' => true,
                'original_size' => $originalSize,
                'original_format' => $originalFormat,
                'optimized_images' => $optimizedImages,
                'total_savings' => $this->calculateTotalSavings($originalSize, $optimizedImages)
            ];

        } catch (\Exception $e) {
            Log::error('Image optimization failed', [
                'image_path' => $imagePath,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Convert image to WebP format
     * 
     * @param string $imagePath
     * @param array $options
     * @return string|null
     */
    private function convertToWebP(string $imagePath, array $options = []): ?string
    {
        try {
            $image = Image::make($imagePath);
            $quality = $options['webp_quality'] ?? self::DEFAULT_QUALITY['webp'];
            
            // Apply optimizations
            $this->applyImageOptimizations($image, $options);
            
            $webpPath = $this->generateOptimizedPath($imagePath, 'webp');
            $image->encode('webp', $quality)->save($webpPath);
            
            return $webpPath;
            
        } catch (\Exception $e) {
            Log::warning('WebP conversion failed', [
                'image_path' => $imagePath,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Optimize image in its original format
     * 
     * @param string $imagePath
     * @param string $format
     * @param array $options
     * @return string
     */
    private function optimizeImage(string $imagePath, string $format, array $options = []): string
    {
        $image = Image::make($imagePath);
        $quality = $options['quality'] ?? self::DEFAULT_QUALITY[$format];
        
        // Apply optimizations
        $this->applyImageOptimizations($image, $options);
        
        $optimizedPath = $this->generateOptimizedPath($imagePath, $format);
        
        // Format-specific optimizations
        switch ($format) {
            case 'jpg':
            case 'jpeg':
                $image->encode('jpg', $quality)->save($optimizedPath);
                break;
                
            case 'png':
                // PNG compression level (0-9)
                $image->encode('png', $quality)->save($optimizedPath);
                break;
                
            case 'webp':
                $image->encode('webp', $quality)->save($optimizedPath);
                break;
                
            case 'gif':
                // GIF optimization (limited options)
                $image->encode('gif')->save($optimizedPath);
                break;
                
            default:
                $image->save($optimizedPath);
        }
        
        return $optimizedPath;
    }

    /**
     * Apply common image optimizations
     * 
     * @param \Intervention\Image\Image $image
     * @param array $options
     * @return void
     */
    private function applyImageOptimizations($image, array $options = []): void
    {
        // Resize if max dimensions specified
        if (isset($options['max_width']) || isset($options['max_height'])) {
            $maxWidth = $options['max_width'] ?? null;
            $maxHeight = $options['max_height'] ?? null;
            
            $image->resize($maxWidth, $maxHeight, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize(); // Prevent upsizing
            });
        }

        // Auto-orient based on EXIF data
        if ($options['auto_orient'] ?? true) {
            $image->orientate();
        }

        // Sharpen if requested
        if ($options['sharpen'] ?? false) {
            $image->sharpen($options['sharpen_amount'] ?? 10);
        }

        // Apply filters
        if (isset($options['filters'])) {
            foreach ($options['filters'] as $filter => $value) {
                switch ($filter) {
                    case 'brightness':
                        $image->brightness($value);
                        break;
                    case 'contrast':
                        $image->contrast($value);
                        break;
                    case 'gamma':
                        $image->gamma($value);
                        break;
                }
            }
        }
    }

    /**
     * Generate responsive image sizes
     * 
     * @param string $imagePath
     * @param array $options
     * @return array
     */
    private function generateResponsiveSizes(string $imagePath, array $options = []): array
    {
        $sizes = $options['sizes'] ?? [320, 640, 768, 1024, 1280, 1920];
        $responsiveImages = [];
        
        $originalImage = Image::make($imagePath);
        $originalWidth = $originalImage->width();
        
        foreach ($sizes as $width) {
            // Skip if target width is larger than original
            if ($width > $originalWidth) {
                continue;
            }
            
            $resizedImage = clone $originalImage;
            $resizedImage->resize($width, null, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            });
            
            $format = $this->getImageFormat($imagePath);
            $quality = $options['quality'] ?? self::DEFAULT_QUALITY[$format];
            
            $responsivePath = $this->generateResponsivePath($imagePath, $width, $format);
            $resizedImage->encode($format, $quality)->save($responsivePath);
            
            $responsiveImages[$width] = [
                'path' => $responsivePath,
                'width' => $width,
                'height' => $resizedImage->height(),
                'size' => filesize($responsivePath)
            ];
        }
        
        return $responsiveImages;
    }

    /**
     * Check if WebP is supported
     * 
     * @return bool
     */
    private function supportsWebP(): bool
    {
        return function_exists('imagewebp') && 
               (isset($_SERVER['HTTP_ACCEPT']) && 
                strpos($_SERVER['HTTP_ACCEPT'], 'image/webp') !== false);
    }

    /**
     * Get image format from file path
     * 
     * @param string $imagePath
     * @return string
     */
    private function getImageFormat(string $imagePath): string
    {
        $extension = strtolower(pathinfo($imagePath, PATHINFO_EXTENSION));
        return $extension === 'jpeg' ? 'jpg' : $extension;
    }

    /**
     * Generate optimized file path
     * 
     * @param string $originalPath
     * @param string $format
     * @return string
     */
    private function generateOptimizedPath(string $originalPath, string $format): string
    {
        $pathInfo = pathinfo($originalPath);
        $directory = $pathInfo['dirname'];
        $filename = $pathInfo['filename'];
        
        return $directory . '/' . $filename . '_optimized.' . $format;
    }

    /**
     * Generate responsive image path
     * 
     * @param string $originalPath
     * @param int $width
     * @param string $format
     * @return string
     */
    private function generateResponsivePath(string $originalPath, int $width, string $format): string
    {
        $pathInfo = pathinfo($originalPath);
        $directory = $pathInfo['dirname'];
        $filename = $pathInfo['filename'];
        
        return $directory . '/' . $filename . '_' . $width . 'w.' . $format;
    }

    /**
     * Calculate compression ratio
     * 
     * @param int $originalSize
     * @param int $compressedSize
     * @return float
     */
    private function calculateCompressionRatio(int $originalSize, int $compressedSize): float
    {
        if ($originalSize === 0) {
            return 0;
        }
        
        return round((($originalSize - $compressedSize) / $originalSize) * 100, 2);
    }

    /**
     * Calculate total savings across all optimized images
     * 
     * @param int $originalSize
     * @param array $optimizedImages
     * @return array
     */
    private function calculateTotalSavings(int $originalSize, array $optimizedImages): array
    {
        $totalOptimizedSize = 0;
        $bestCompression = 0;
        
        foreach ($optimizedImages as $type => $images) {
            if ($type === 'responsive') {
                continue; // Skip responsive images for total calculation
            }
            
            if (is_array($images) && isset($images['size'])) {
                $totalOptimizedSize += $images['size'];
                $bestCompression = max($bestCompression, $images['compression_ratio']);
            }
        }
        
        return [
            'total_size_reduction' => $originalSize - $totalOptimizedSize,
            'total_compression_ratio' => $this->calculateCompressionRatio($originalSize, $totalOptimizedSize),
            'best_compression_ratio' => $bestCompression
        ];
    }
}
