<x-layouts.app>
    {{-- JSON-LD Structured Data for Converter Tool --}}
    @push('head')
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "{{ $headerTitle }}",
        "description": "{{ $headerSubtitle }}",
        "url": "{{ url()->current() }}",
        "applicationCategory": "UtilityApplication",
        "operatingSystem": "Web Browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "featureList": [
            "Convert images to {{ strtoupper($format) }} format",
            "Free online conversion",
            "No registration required",
            "Batch processing support",
            "High quality output"
        ],
        "screenshot": "{{ asset('images/converter-screenshot.png') }}",
        "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.8",
            "reviewCount": "1250"
        }
    }
    </script>
    @endpush

    <div id="converter" class="space-y-8">
        {{-- Breadcrumb Navigation --}}
        <nav aria-label="{{ __('Breadcrumb') }}" class="mb-6">
            <ol class="flex items-center space-x-2 text-sm text-gray-600" itemscope itemtype="https://schema.org/BreadcrumbList">
                <li itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                    <a href="{{ url('/') }}" class="hover:text-blue-600 transition-colors duration-200" itemprop="item">
                        <span itemprop="name">{{ __('Home') }}</span>
                    </a>
                    <meta itemprop="position" content="1" />
                </li>
                <li class="flex items-center">
                    <svg class="w-4 h-4 mx-2 text-gray-400 {{ currentLocale() === 'ar' ? 'rotate-180' : '' }}" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                        <span itemprop="name" class="text-gray-900 font-medium">{{ $converter['entryTitle'] ?? $headerTitle }}</span>
                        <meta itemprop="position" content="2" />
                    </span>
                </li>
            </ol>
        </nav>

        {{-- Hero Section with Enhanced SEO --}}
        <header class="mb-10 text-center space-y-4" role="banner">
            <h1 class="mb-4 text-3xl md:text-4xl lg:text-5xl font-bold leading-tight text-gray-900">
                {{ $headerTitle }}
            </h1>
            <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                {{ $headerSubtitle }}
            </p>

            {{-- Quick Stats --}}
            <div class="flex flex-wrap justify-center gap-6 mt-6 text-sm text-gray-500">
                <div class="flex items-center">
                    <svg class="w-4 h-4 {{ currentLocale() === 'ar' ? 'ml-1' : 'mr-1' }} text-green-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    {{ __('Free & Secure') }}
                </div>
                <div class="flex items-center">
                    <svg class="w-4 h-4 {{ currentLocale() === 'ar' ? 'ml-1' : 'mr-1' }} text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    {{ __('Batch Processing') }}
                </div>
                <div class="flex items-center">
                    <svg class="w-4 h-4 {{ currentLocale() === 'ar' ? 'ml-1' : 'mr-1' }} text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"></path>
                    </svg>
                    {{ __('High Quality') }}
                </div>
            </div>
        </header>
        @if ($showTopAd)
            <div class="mb-8 top-ad">
                {!! $adSettings->topAdCode !!}
            </div>
        @endif
        <livewire:uploader :slug="$slug" :format="$format" />
        @if ($showMiddleAd)
            <div class="mb-8 middle-ad">
                {!! $adSettings->middleAdCode !!}
            </div>
        @endif
        <section class="mb-8 prose about">
            {!! $description !!}
        </section>
        @php
            $relatedConverters = collect(converters())
                ->filter(fn($converter) => $converter['format'] !== $format && $converter['enabled'])
                ->take(12) // Limit to 12 for better performance
                ->toArray();
        @endphp

        @if (!empty($relatedConverters))
            <section class="mb-8 converters-links" role="complementary" aria-labelledby="related-converters">
                <h2 id="related-converters" class="mb-6 text-2xl font-bold text-gray-900">
                    {{ __('Related Image Converters') }}
                </h2>
                <p class="mb-6 text-gray-600">
                    {{ __('Explore other image conversion tools to meet all your needs') }}
                </p>

                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    @foreach ($relatedConverters as $converter)
                        <article class="group">
                            <a class="block p-4 bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-blue-300 focus:ring-opacity-50"
                               href="{{ localizedRoute('resolver', $slugs->{$converter['name']}) }}"
                               aria-label="{{ __('Convert images to :format', ['format' => strtoupper($converter['format'])]) }}">

                                {{-- Icon --}}
                                <div class="flex items-center justify-center w-12 h-12 mx-auto mb-3 bg-blue-50 rounded-lg group-hover:bg-blue-100 transition-colors duration-300">
                                    <x-dynamic-component :component="$converter['icon']"
                                                       class="w-6 h-6 text-blue-600" />
                                </div>

                                {{-- Title --}}
                                <h3 class="text-sm font-semibold text-center text-gray-900 group-hover:text-blue-600 transition-colors duration-300 leading-tight">
                                    {{ $converter['entryTitle'] }}
                                </h3>

                                {{-- Format Badge --}}
                                <span class="inline-block mt-2 px-2 py-1 text-xs font-medium uppercase tracking-wide text-blue-800 bg-blue-100 rounded-full mx-auto block text-center w-fit">
                                    {{ $converter['format'] }}
                                </span>

                                {{-- Views count if available --}}
                                @if (isset($converter['views']) && $converter['views'] > 0)
                                    <div class="flex items-center justify-center mt-2 text-xs text-gray-500">
                                        <svg class="w-3 h-3 {{ currentLocale() === 'ar' ? 'ml-1' : 'mr-1' }}" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                                        </svg>
                                        {{ number_format($converter['views']) }}
                                    </div>
                                @endif
                            </a>
                        </article>
                    @endforeach
                </div>

                {{-- View All Link --}}
                <div class="text-center mt-6">
                    <a href="{{ url('/') }}"
                       class="inline-flex items-center px-6 py-3 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors duration-300 focus:outline-none focus:ring-4 focus:ring-blue-300 focus:ring-opacity-50">
                        {{ __('View All Converters') }}
                        <svg class="w-4 h-4 {{ currentLocale() === 'ar' ? 'mr-2' : 'ml-2' }}" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"/>
                        </svg>
                    </a>
                </div>
            </section>
        @endif
        @if ($showBottomAd)
            <div class="mb-8 bottom-ad">
                {!! $adSettings->bottomAdCode !!}
            </div>
        @endif
        @if ($showShareButtons)
            <x-share-buttons />
        @endif
    </div>
</x-layouts.app>
