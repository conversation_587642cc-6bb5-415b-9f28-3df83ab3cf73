/* RTL Support and Enhanced Styling for Image Converter */

/* RTL Base Styles */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] body {
    font-family: 'Roboto', 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
}

/* RTL Navigation */
[dir="rtl"] .navbar {
    direction: rtl;
}

[dir="rtl"] .navbar-nav {
    flex-direction: row-reverse;
}

[dir="rtl"] .dropdown-menu {
    right: 0;
    left: auto;
}

/* RTL Grid and Flexbox */
[dir="rtl"] .grid {
    direction: rtl;
}

[dir="rtl"] .flex {
    direction: rtl;
}

/* RTL Cards */
[dir="rtl"] .card {
    text-align: center;
}

[dir="rtl"] .card-views {
    left: 0.75rem;
    right: auto;
}

/* RTL Form Elements */
[dir="rtl"] input,
[dir="rtl"] textarea,
[dir="rtl"] select {
    text-align: right;
}

[dir="rtl"] .filepond {
    direction: rtl;
}

/* RTL Buttons */
[dir="rtl"] .btn {
    direction: rtl;
}

[dir="rtl"] .btn svg {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* Performance Optimizations */
.card {
    will-change: transform;
    backface-visibility: hidden;
    transform: translateZ(0);
}

.card:hover {
    transform: translateZ(0) scale(1.05);
}

/* Smooth Animations */
.transition-all {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-colors {
    transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
}

.transition-transform {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Loading States */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Focus States for Accessibility */
.focus\:ring-4:focus {
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5);
}

.focus\:ring-blue-300:focus {
    box-shadow: 0 0 0 4px rgba(147, 197, 253, 0.5);
}

.focus\:ring-red-300:focus {
    box-shadow: 0 0 0 4px rgba(252, 165, 165, 0.5);
}

.focus\:ring-green-300:focus {
    box-shadow: 0 0 0 4px rgba(134, 239, 172, 0.5);
}

/* Screen Reader Only */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.focus\:not-sr-only:focus {
    position: static;
    width: auto;
    height: auto;
    padding: inherit;
    margin: inherit;
    overflow: visible;
    clip: auto;
    white-space: normal;
}

/* Enhanced Modal Styles */
.modal {
    backdrop-filter: blur(4px);
}

.modal-box {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Improved File Upload Area */
.filepond--root {
    border-radius: 0.75rem;
    border: 2px dashed #d1d5db;
    transition: border-color 0.3s ease;
}

.filepond--root:hover {
    border-color: #3b82f6;
}

.filepond--root.filepond--hopper {
    border-color: #10b981;
    background-color: #f0fdf4;
}

/* Enhanced Button Styles */
.btn {
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

/* Responsive Design Improvements */
@media (max-width: 640px) {
    .card {
        padding: 1rem;
    }
    
    .card-icon {
        width: 3rem;
        height: 3rem;
    }
    
    .card-icon svg {
        width: 2rem;
        height: 2rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .skeleton {
        background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
        background-size: 200% 100%;
    }
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .card {
        break-inside: avoid;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .skeleton {
        animation: none;
        background: #f0f0f0;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid;
    }
    
    .btn {
        border: 2px solid;
    }
}
