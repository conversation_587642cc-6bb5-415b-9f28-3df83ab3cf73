<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="format-detection" content="telephone=no">
    <meta name="theme-color" content="{{ $styleSettings->navBgColor ?? '#3b82f6' }}">

    {{-- Robots meta tag --}}
    @if ($noIndex)
        <meta name="robots" content="noindex,nofollow">
    @else
        <meta name="robots" content="index,follow,max-image-preview:large,max-snippet:-1,max-video-preview:-1">
    @endif

    {{-- Favicon and app icons --}}
    <link rel="shortcut icon" href="{{ asset('/storage/favicon.ico') }}" type="image/x-icon">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('/storage/favicon.ico') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('/storage/favicon.ico') }}">
    <link rel="apple-touch-icon" href="{{ asset('/storage/favicon.ico') }}">

    {{-- PWA Manifest --}}
    <link rel="manifest" href="{{ asset('/manifest.json') }}">

    {{-- iOS specific meta tags --}}
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Image Converter">

    {{-- Windows specific meta tags --}}
    <meta name="msapplication-TileColor" content="{{ $styleSettings->navBgColor ?? '#3b82f6' }}">
    <meta name="msapplication-config" content="{{ asset('/browserconfig.xml') }}">

    {{-- SEO Meta Tags --}}
    {!! SEOMeta::generate() !!}

    {{-- Open Graph Tags --}}
    {!! OpenGraph::generate() !!}

    {{-- Twitter Card Tags --}}
    {!! Twitter::generate() !!}

    {{-- JSON-LD Structured Data --}}
    {!! JsonLd::generate() !!}

    {{-- Language alternatives --}}
    @foreach(locales() as $localeCode => $properties)
        <link rel="alternate" hreflang="{{ $localeCode }}" href="{{ LaravelLocalization::getLocalizedURL($localeCode, null, [], true) }}">
    @endforeach
    <link rel="alternate" hreflang="x-default" href="{{ LaravelLocalization::getLocalizedURL('en', null, [], true) }}">

    {{-- Canonical URL --}}
    <link rel="canonical" href="{{ url()->current() }}">

    {{-- DNS Prefetch for external resources --}}
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//fonts.gstatic.com">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">

    {{-- Preconnect for critical resources --}}
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    {{-- Optimized font loading with font-display: swap --}}
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    {{-- Font Awesome with integrity check --}}
    <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css"
          integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA=="
          crossorigin="anonymous" referrerpolicy="no-referrer">

    {{-- Vite assets with preload --}}
    @vite(['resources/css/app.css', 'resources/css/share-buttons.css', 'resources/css/rtl-support.css', 'resources/js/app.js'])
    @livewireStyles
    @if (!empty($generalSettings->headerTags))
        {!! $generalSettings->headerTags !!}
    @endif
    @if (!empty($generalSettings->analyticsId))
        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id={{ $generalSettings->analyticsId }}"></script>
        <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());

            gtag('config', '{{ $generalSettings->analyticsId }}');
        </script>
    @endif
    <style>
        [x-cloak] {
            display: none !important;
        }

        :root {
            --bg-color: {{ $styleSettings->bgColor }};
            --text-color: {{ $styleSettings->textColor }};
            --nav-bg-color: {{ $styleSettings->navBgColor }};
            --nav-text-color: {{ $styleSettings->navTextColor }};
            --uploader-bg-color: {{ $styleSettings->uploaderBgColor }};
            --uploader-text-color: {{ $styleSettings->uploaderTextColor }};
            --uploader-bar-text-color: {{ $styleSettings->uploadBarTextColor }};
            --uploader-bar-success-bg-color: {{ $styleSettings->uploadBarSuccessBgColor }};
            --uploader-bar-error-bg-color: {{ $styleSettings->uploadBarErrorBgColor }};
            --browse-btn-bg-color: {{ $styleSettings->browseBtnBgColor }};
            --browse-btn-text-color: {{ $styleSettings->browseBtnTextColor }};
            --convert-btn-bg-color: {{ $styleSettings->convertBtnBgColor }};
            --convert-btn-text-color: {{ $styleSettings->convertBtnTextColor }};
            --card-bg-color: {{ $styleSettings->cardBgColor }};
            --card-text-color: {{ $styleSettings->cardTextColor }};
            --card-hover-bg-color: {{ $styleSettings->cardHoverBgColor }};
            --card-hover-text-color: {{ $styleSettings->cardHoverTextColor }};
        }

        {{ $styleSettings->customCSS }}
    </style>
</head>
