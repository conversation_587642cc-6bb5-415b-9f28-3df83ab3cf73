/**
 * Main Application JavaScript File
 * Enhanced Image Converter with Performance Optimizations
 */

import './bootstrap';
import './performance-enhancements';

// Alpine.js for reactive components
import Alpine from 'alpinejs';

// FilePond for file uploads
import * as FilePond from 'filepond';
import FilePondPluginImagePreview from 'filepond-plugin-image-preview';
import FilePondPluginFileValidateType from 'filepond-plugin-file-validate-type';
import FilePondPluginFileValidateSize from 'filepond-plugin-file-validate-size';
import FilePondPluginImageExifOrientation from 'filepond-plugin-image-exif-orientation';

// Register FilePond plugins
FilePond.registerPlugin(
    FilePondPluginImagePreview,
    FilePondPluginFileValidateType,
    FilePondPluginFileValidateSize,
    FilePondPluginImageExifOrientation
);

// Make FilePond available globally
window.FilePond = FilePond;

// Alpine.js data components
Alpine.data('filepond', () => ({
    // Component state
    showPlaceHolder: true,
    showUploader: false,
    showConvertBtn: false,
    isConvertBtnDisabled: true,
    pond: null,
    
    // Initialize FilePond
    init() {
        this.initializeFilePond();
        this.setupEventListeners();
        
        // Hide placeholder after initialization
        setTimeout(() => {
            this.showPlaceHolder = false;
            this.showUploader = true;
        }, 500);
    },

    initializeFilePond() {
        const pondElement = this.$refs.filepond;
        if (!pondElement) return;

        // Get configuration from Livewire component
        const config = window.uploaderConfig || {};
        
        this.pond = FilePond.create(pondElement, {
            // Server configuration
            server: {
                process: config.routes?.upload || '/upload',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
                }
            },
            
            // File validation
            acceptedFileTypes: [
                'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 
                'image/webp', 'image/bmp', 'image/tiff', 'image/svg+xml',
                'image/x-icon', 'image/heic', 'image/heif', 'image/avif'
            ],
            maxFileSize: config.maxFileSize || '10MB',
            maxFiles: config.maxFiles || 10,
            
            // UI configuration
            allowMultiple: true,
            allowReorder: true,
            allowImagePreview: true,
            imagePreviewHeight: 170,
            imageCropAspectRatio: '1:1',
            imageResizeTargetWidth: 200,
            imageResizeTargetHeight: 200,
            
            // Localization
            labelIdle: config.locales?.dragNDrop || 'Drag & Drop your images or <span class="filepond--label-action">Browse</span>',
            labelInvalidField: config.locales?.labelInvalidField || 'Field contains invalid files',
            labelFileWaitingForSize: config.locales?.labelFileWaitingForSize || 'Waiting for size',
            labelFileSizeNotAvailable: config.locales?.labelFileSizeNotAvailable || 'Size not available',
            labelFileLoading: config.locales?.labelFileLoading || 'Loading',
            labelFileLoadError: config.locales?.labelFileLoadError || 'Error during load',
            labelFileProcessing: config.locales?.labelFileProcessing || 'Uploading',
            labelFileProcessingComplete: config.locales?.labelFileProcessingComplete || 'Upload complete',
            labelFileProcessingAborted: config.locales?.labelFileProcessingAborted || 'Upload cancelled',
            labelFileProcessingRevertError: config.locales?.labelFileProcessingRevertError || 'Error during revert',
            labelFileRemoveError: config.locales?.labelFileRemoveError || 'Error during remove',
            labelTapToCancel: config.locales?.labelTapToCancel || 'tap to cancel',
            labelTapToRetry: config.locales?.labelTapToRetry || 'tap to retry',
            labelTapToUndo: config.locales?.labelTapToUndo || 'tap to undo',
            labelButtonRemoveItem: config.locales?.labelButtonRemoveItem || 'Remove',
            labelButtonAbortItemLoad: config.locales?.labelButtonAbortItemLoad || 'Abort',
            labelButtonRetryItemLoad: config.locales?.labelButtonRetryItemLoad || 'Retry',
            labelButtonAbortItemProcessing: config.locales?.labelButtonAbortItemProcessing || 'Cancel',
            labelButtonUndoItemProcessing: config.locales?.labelButtonUndoItemProcessing || 'Undo',
            labelButtonRetryItemProcessing: config.locales?.labelButtonRetryItemProcessing || 'Retry',
            labelButtonProcessItem: config.locales?.labelButtonProcessItem || 'Upload',
            labelFileTypeNotAllowed: config.locales?.labelFileTypeNotAllowed || 'File of invalid type',
            fileValidateTypeLabelExpectedTypes: config.locales?.fileValidateTypeLabelExpectedTypes || 'Expects {allButLastType} or {lastType}',
            labelMaxFileSizeExceeded: config.locales?.labelMaxFileSizeExceeded || 'File is too large',
            labelMaxFileSize: config.locales?.labelMaxFileSize || 'Maximum file size is {filesize}',
            
            // Event handlers
            onaddfile: (error, file) => {
                if (error) {
                    console.error('Error adding file:', error);
                    return;
                }
                this.updateConvertButton();
                this.trackEvent('file_added', { filename: file.filename });
            },
            
            onremovefile: (error, file) => {
                if (error) {
                    console.error('Error removing file:', error);
                    return;
                }
                this.updateConvertButton();
                this.trackEvent('file_removed', { filename: file.filename });
            },
            
            onprocessfile: (error, file) => {
                if (error) {
                    console.error('Error processing file:', error);
                    this.showErrorNotification(error.body || 'Error processing file');
                    return;
                }
                this.updateConvertButton();
                this.trackEvent('file_processed', { filename: file.filename });
            },
            
            onprocessfiles: () => {
                this.updateConvertButton();
                this.trackEvent('all_files_processed');
            }
        });
    },

    setupEventListeners() {
        // Listen for Livewire events
        document.addEventListener('livewire:load', () => {
            this.updateConvertButton();
        });
        
        // Listen for configuration updates
        document.addEventListener('uploader-config-updated', (event) => {
            if (this.pond && event.detail) {
                this.pond.setOptions(event.detail);
            }
        });
    },

    updateConvertButton() {
        if (!this.pond) return;
        
        const files = this.pond.getFiles();
        const processedFiles = files.filter(file => file.status === FilePond.FileStatus.PROCESSING_COMPLETE);
        
        this.showConvertBtn = processedFiles.length > 0;
        this.isConvertBtnDisabled = processedFiles.length === 0;
    },

    convert() {
        if (!this.pond) return;
        
        const files = this.pond.getFiles();
        const processedFiles = files.filter(file => file.status === FilePond.FileStatus.PROCESSING_COMPLETE);
        
        if (processedFiles.length === 0) {
            this.showErrorNotification('No files to convert');
            return;
        }
        
        // Extract file IDs for Livewire
        const fileIds = processedFiles.map(file => file.serverId);
        
        // Track conversion start
        this.trackEvent('conversion_started', { 
            file_count: fileIds.length,
            format: window.converterFormat || 'unknown'
        });
        
        // Call Livewire method
        if (window.Livewire) {
            window.Livewire.emit('convert', fileIds);
        }
    },

    showErrorNotification(message) {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = 'fixed top-4 right-4 bg-red-500 text-white p-4 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
        toast.innerHTML = `
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <span>${message}</span>
            </div>
        `;
        
        document.body.appendChild(toast);
        
        // Animate in
        setTimeout(() => {
            toast.classList.remove('translate-x-full');
        }, 100);
        
        // Remove after 5 seconds
        setTimeout(() => {
            toast.classList.add('translate-x-full');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 5000);
    },

    trackEvent(eventName, properties = {}) {
        // Google Analytics 4 tracking
        if (typeof gtag !== 'undefined') {
            gtag('event', eventName, {
                event_category: 'image_converter',
                ...properties
            });
        }
        
        // Console logging for development
        if (process.env.NODE_ENV === 'development') {
            console.log('Event tracked:', eventName, properties);
        }
    },

    destroy() {
        if (this.pond) {
            this.pond.destroy();
            this.pond = null;
        }
    }
}));

// Initialize Alpine.js
window.Alpine = Alpine;
Alpine.start();

// Service Worker registration for PWA capabilities
if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}

// Export for use in other modules
export { Alpine, FilePond };
