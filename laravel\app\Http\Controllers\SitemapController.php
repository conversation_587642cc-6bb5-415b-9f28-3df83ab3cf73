<?php

namespace App\Http\Controllers;

use App\Models\Post;
use App\Models\Page;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;

/**
 * Enhanced Sitemap Controller with multi-language support
 * 
 * Generates XML sitemaps for better SEO with:
 * - Multi-language support
 * - Dynamic content (posts, pages, converters)
 * - Proper priority and frequency settings
 * - Caching for performance
 */
class SitemapController extends Controller
{
    /**
     * Generate main sitemap index
     * 
     * @return Response
     */
    public function index(): Response
    {
        $cacheKey = getCacheKey('sitemap_index');
        
        $content = Cache::remember($cacheKey, 3600, function () {
            $sitemaps = [];
            
            // Add main sitemap
            $sitemaps[] = [
                'loc' => url('/sitemap-main.xml'),
                'lastmod' => now()->toISOString(),
            ];
            
            // Add posts sitemap if posts exist
            if (Post::count() > 0) {
                $sitemaps[] = [
                    'loc' => url('/sitemap-posts.xml'),
                    'lastmod' => Post::latest('updated_at')->first()?->updated_at?->toISOString() ?? now()->toISOString(),
                ];
            }
            
            // Add pages sitemap if pages exist
            if (Page::count() > 0) {
                $sitemaps[] = [
                    'loc' => url('/sitemap-pages.xml'),
                    'lastmod' => Page::latest('updated_at')->first()?->updated_at?->toISOString() ?? now()->toISOString(),
                ];
            }
            
            // Add converters sitemap
            $sitemaps[] = [
                'loc' => url('/sitemap-converters.xml'),
                'lastmod' => now()->toISOString(),
            ];
            
            return $this->generateSitemapIndex($sitemaps);
        });
        
        return response($content, 200, [
            'Content-Type' => 'application/xml; charset=utf-8',
            'Cache-Control' => 'public, max-age=3600',
        ]);
    }

    /**
     * Generate main sitemap (homepage and static pages)
     * 
     * @return Response
     */
    public function main(): Response
    {
        $cacheKey = getCacheKey('sitemap_main');
        
        $content = Cache::remember($cacheKey, 3600, function () {
            $urls = [];
            
            // Add homepage for each language
            foreach (LaravelLocalization::getSupportedLocales() as $localeCode => $properties) {
                $urls[] = [
                    'loc' => LaravelLocalization::getLocalizedURL($localeCode, '/'),
                    'lastmod' => now()->toISOString(),
                    'changefreq' => 'daily',
                    'priority' => '1.0',
                    'alternates' => $this->getAlternateUrls('/'),
                ];
            }
            
            return $this->generateUrlset($urls);
        });
        
        return response($content, 200, [
            'Content-Type' => 'application/xml; charset=utf-8',
            'Cache-Control' => 'public, max-age=3600',
        ]);
    }

    /**
     * Generate converters sitemap
     * 
     * @return Response
     */
    public function converters(): Response
    {
        $cacheKey = getCacheKey('sitemap_converters');
        
        $content = Cache::remember($cacheKey, 3600, function () {
            $urls = [];
            $converters = collect(converters())->where('enabled', true);
            $slugs = app(\App\Settings\ConvertersSlugsSettings::class);
            
            foreach ($converters as $converter) {
                $slug = $slugs->{$converter['name']};
                
                foreach (LaravelLocalization::getSupportedLocales() as $localeCode => $properties) {
                    $url = LaravelLocalization::getLocalizedURL($localeCode, "/converter/{$slug}");
                    
                    $urls[] = [
                        'loc' => $url,
                        'lastmod' => now()->toISOString(),
                        'changefreq' => 'weekly',
                        'priority' => '0.8',
                        'alternates' => $this->getAlternateUrls("/converter/{$slug}"),
                    ];
                }
            }
            
            return $this->generateUrlset($urls);
        });
        
        return response($content, 200, [
            'Content-Type' => 'application/xml; charset=utf-8',
            'Cache-Control' => 'public, max-age=3600',
        ]);
    }

    /**
     * Generate posts sitemap
     * 
     * @return Response
     */
    public function posts(): Response
    {
        $cacheKey = getCacheKey('sitemap_posts');
        
        $content = Cache::remember($cacheKey, 1800, function () { // 30 minutes cache
            $urls = [];

            // Get posts for each language separately
            foreach (LaravelLocalization::getSupportedLocales() as $localeCode => $properties) {
                $posts = Post::forLocale($localeCode)
                           ->published()
                           ->orderBy('updated_at', 'desc')
                           ->get();

                foreach ($posts as $post) {
                    $url = LaravelLocalization::getLocalizedURL($localeCode, "/blog/{$post->slug}");

                    $urls[] = [
                        'loc' => $url,
                        'lastmod' => $post->updated_at->toISOString(),
                        'changefreq' => 'monthly',
                        'priority' => $post->featured ? '0.8' : '0.6',
                        'alternates' => $this->getPostAlternateUrls($post),
                    ];
                }
            }

            return $this->generateUrlset($urls);
        });
        
        return response($content, 200, [
            'Content-Type' => 'application/xml; charset=utf-8',
            'Cache-Control' => 'public, max-age=1800',
        ]);
    }

    /**
     * Generate pages sitemap
     * 
     * @return Response
     */
    public function pages(): Response
    {
        $cacheKey = getCacheKey('sitemap_pages');
        
        $content = Cache::remember($cacheKey, 1800, function () { // 30 minutes cache
            $urls = [];
            $pages = Page::published()->orderBy('updated_at', 'desc')->get();
            
            foreach ($pages as $page) {
                foreach (LaravelLocalization::getSupportedLocales() as $localeCode => $properties) {
                    $url = LaravelLocalization::getLocalizedURL($localeCode, "/page/{$page->slug}");
                    
                    $urls[] = [
                        'loc' => $url,
                        'lastmod' => $page->updated_at->toISOString(),
                        'changefreq' => 'monthly',
                        'priority' => '0.5',
                        'alternates' => $this->getAlternateUrls("/page/{$page->slug}"),
                    ];
                }
            }
            
            return $this->generateUrlset($urls);
        });
        
        return response($content, 200, [
            'Content-Type' => 'application/xml; charset=utf-8',
            'Cache-Control' => 'public, max-age=1800',
        ]);
    }

    /**
     * Generate sitemap index XML
     * 
     * @param array $sitemaps
     * @return string
     */
    private function generateSitemapIndex(array $sitemaps): string
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;
        $xml .= '<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . PHP_EOL;
        
        foreach ($sitemaps as $sitemap) {
            $xml .= '  <sitemap>' . PHP_EOL;
            $xml .= '    <loc>' . htmlspecialchars($sitemap['loc']) . '</loc>' . PHP_EOL;
            $xml .= '    <lastmod>' . $sitemap['lastmod'] . '</lastmod>' . PHP_EOL;
            $xml .= '  </sitemap>' . PHP_EOL;
        }
        
        $xml .= '</sitemapindex>' . PHP_EOL;
        
        return $xml;
    }

    /**
     * Generate urlset XML
     * 
     * @param array $urls
     * @return string
     */
    private function generateUrlset(array $urls): string
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">' . PHP_EOL;
        
        foreach ($urls as $url) {
            $xml .= '  <url>' . PHP_EOL;
            $xml .= '    <loc>' . htmlspecialchars($url['loc']) . '</loc>' . PHP_EOL;
            $xml .= '    <lastmod>' . $url['lastmod'] . '</lastmod>' . PHP_EOL;
            $xml .= '    <changefreq>' . $url['changefreq'] . '</changefreq>' . PHP_EOL;
            $xml .= '    <priority>' . $url['priority'] . '</priority>' . PHP_EOL;
            
            // Add alternate language versions
            if (isset($url['alternates'])) {
                foreach ($url['alternates'] as $alternate) {
                    $xml .= '    <xhtml:link rel="alternate" hreflang="' . $alternate['hreflang'] . '" href="' . htmlspecialchars($alternate['href']) . '" />' . PHP_EOL;
                }
            }
            
            $xml .= '  </url>' . PHP_EOL;
        }
        
        $xml .= '</urlset>' . PHP_EOL;
        
        return $xml;
    }

    /**
     * Get alternate language URLs for a given path
     * 
     * @param string $path
     * @return array
     */
    private function getAlternateUrls(string $path): array
    {
        $alternates = [];
        
        foreach (LaravelLocalization::getSupportedLocales() as $localeCode => $properties) {
            $alternates[] = [
                'hreflang' => $localeCode,
                'href' => LaravelLocalization::getLocalizedURL($localeCode, $path),
            ];
        }
        
        // Add x-default for main language
        $alternates[] = [
            'hreflang' => 'x-default',
            'href' => LaravelLocalization::getLocalizedURL('en', $path),
        ];
        
        return $alternates;
    }

    /**
     * Get alternate language URLs for a specific post
     *
     * @param Post $post
     * @return array
     */
    private function getPostAlternateUrls(Post $post): array
    {
        $alternates = [];

        // Find posts with same slug in other languages
        $relatedPosts = Post::where('slug', $post->slug)
                           ->where('id', '!=', $post->id)
                           ->published()
                           ->get();

        // Add current post
        $alternates[] = [
            'hreflang' => $post->locale,
            'href' => LaravelLocalization::getLocalizedURL($post->locale, "/blog/{$post->slug}"),
        ];

        // Add related posts in other languages
        foreach ($relatedPosts as $relatedPost) {
            $alternates[] = [
                'hreflang' => $relatedPost->locale,
                'href' => LaravelLocalization::getLocalizedURL($relatedPost->locale, "/blog/{$relatedPost->slug}"),
            ];
        }

        // Add x-default (fallback to English if available)
        $defaultPost = $relatedPosts->where('locale', 'en')->first() ?: $post;
        $alternates[] = [
            'hreflang' => 'x-default',
            'href' => LaravelLocalization::getLocalizedURL($defaultPost->locale, "/blog/{$defaultPost->slug}"),
        ];

        return $alternates;
    }
}
