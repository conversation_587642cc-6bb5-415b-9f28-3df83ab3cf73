import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import { resolve } from 'path';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/css/share-buttons.css',
                'resources/css/rtl-support.css',
                'resources/js/app.js',
                'resources/js/performance-enhancements.js'
            ],
            refresh: true,
        }),
    ],
    
    // Build optimizations
    build: {
        // Generate source maps for debugging
        sourcemap: process.env.NODE_ENV === 'development',
        
        // Optimize bundle size
        rollupOptions: {
            output: {
                // Manual chunk splitting for better caching
                manualChunks: {
                    // Vendor libraries
                    vendor: ['axios', 'lodash'],
                    filepond: ['filepond', 'filepond-plugin-image-preview', 'filepond-plugin-file-validate-type', 'filepond-plugin-file-validate-size'],
                    alpine: ['alpinejs']
                },
                
                // Asset file naming
                assetFileNames: (assetInfo) => {
                    const info = assetInfo.name.split('.');
                    const ext = info[info.length - 1];
                    
                    if (/\.(png|jpe?g|gif|svg|webp|ico)$/i.test(assetInfo.name)) {
                        return `images/[name]-[hash][extname]`;
                    }
                    
                    if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name)) {
                        return `fonts/[name]-[hash][extname]`;
                    }
                    
                    return `assets/[name]-[hash][extname]`;
                },
                
                // Chunk file naming
                chunkFileNames: 'js/[name]-[hash].js',
                entryFileNames: 'js/[name]-[hash].js'
            }
        },
        
        // Minification options
        minify: 'terser',
        terserOptions: {
            compress: {
                drop_console: process.env.NODE_ENV === 'production',
                drop_debugger: process.env.NODE_ENV === 'production',
                pure_funcs: process.env.NODE_ENV === 'production' ? ['console.log'] : []
            },
            format: {
                comments: false
            }
        },
        
        // Target modern browsers for better optimization
        target: 'es2020',
        
        // Chunk size warning limit
        chunkSizeWarningLimit: 1000,
        
        // CSS code splitting
        cssCodeSplit: true,
        
        // Asset inlining threshold
        assetsInlineLimit: 4096
    },
    
    // Development server configuration
    server: {
        host: '0.0.0.0',
        port: 5173,
        strictPort: true,
        hmr: {
            host: 'localhost'
        },
        
        // Proxy API requests to Laravel during development
        proxy: {
            '/api': {
                target: 'http://localhost:8000',
                changeOrigin: true
            },
            '/livewire': {
                target: 'http://localhost:8000',
                changeOrigin: true
            },
            '/upload': {
                target: 'http://localhost:8000',
                changeOrigin: true
            }
        }
    },
    
    // Dependency optimization
    optimizeDeps: {
        include: [
            'axios',
            'lodash',
            'alpinejs',
            'filepond',
            'filepond-plugin-image-preview',
            'filepond-plugin-file-validate-type',
            'filepond-plugin-file-validate-size',
            'filepond-plugin-image-exif-orientation'
        ],
        exclude: []
    },
    
    // CSS preprocessing
    css: {
        preprocessorOptions: {
            scss: {
                additionalData: `@import "resources/css/variables.scss";`
            }
        },
        
        // PostCSS configuration
        postcss: {
            plugins: [
                require('tailwindcss'),
                require('autoprefixer'),
                
                // CSS optimization for production
                ...(process.env.NODE_ENV === 'production' ? [
                    require('cssnano')({
                        preset: ['default', {
                            discardComments: { removeAll: true },
                            normalizeWhitespace: true,
                            minifySelectors: true,
                            minifyParams: true
                        }]
                    })
                ] : [])
            ]
        }
    },
    
    // Path resolution
    resolve: {
        alias: {
            '@': resolve(__dirname, 'resources'),
            '@js': resolve(__dirname, 'resources/js'),
            '@css': resolve(__dirname, 'resources/css'),
            '@images': resolve(__dirname, 'resources/images'),
            '@components': resolve(__dirname, 'resources/js/components')
        }
    },
    
    // Environment variables
    define: {
        __APP_VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
        __BUILD_TIME__: JSON.stringify(new Date().toISOString())
    },
    
    // Preview server configuration (for production builds)
    preview: {
        port: 4173,
        strictPort: true,
        host: '0.0.0.0'
    },
    
    // Experimental features
    experimental: {
        renderBuiltUrl(filename, { hostType }) {
            if (hostType === 'js') {
                return { js: `/${filename}` };
            } else {
                return { relative: true };
            }
        }
    },
    
    // Worker configuration
    worker: {
        format: 'es'
    },
    
    // JSON configuration
    json: {
        namedExports: true,
        stringify: false
    }
});
