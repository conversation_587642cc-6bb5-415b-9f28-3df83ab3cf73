<?php

namespace App\Models;

use Cyrilde<PERSON>it\EloquentViewable\Contracts\Viewable;
use CyrildeWit\EloquentViewable\InteractsWithViews;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Page extends Model implements Viewable
{
    use HasFactory, InteractsWithViews;

    protected $removeViewsOnDelete = true;

    protected $casts = [
        'published_at' => 'date',
    ];

    protected $fillable = [
        'title',
        'slug',
        'label',
        'topAd',
        'bottomAd',
        'showShareButtons',
        'content',
        'location',
        'metaDescription',
        'metaKeywords',
        'noIndex',
        'published_at',
        'locale', // Language code (en, ar)
        'meta_title', // SEO title
        'excerpt', // Short description
        'featured_image', // Featured image path
        'page_template', // Template type
        'menu_order', // Order in navigation
        'show_in_menu', // Show in navigation menu
        'show_in_footer', // Show in footer
    ];

    /**
     * Scope to filter pages by locale
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $locale
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForLocale($query, $locale = null)
    {
        $locale = $locale ?: app()->getLocale();
        return $query->where('locale', $locale);
    }

    /**
     * Scope to get published pages
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePublished($query)
    {
        return $query->whereNotNull('published_at')
                    ->where('published_at', '<=', now());
    }

    /**
     * Scope to get pages for navigation menu
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForMenu($query)
    {
        return $query->where('show_in_menu', true)
                    ->orderBy('menu_order');
    }

    /**
     * Scope to get pages for footer
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForFooter($query)
    {
        return $query->where('show_in_footer', true)
                    ->orderBy('menu_order');
    }

    /**
     * Get the page's SEO title or fallback to title
     *
     * @return string
     */
    public function getSeoTitleAttribute(): string
    {
        return $this->meta_title ?: $this->title;
    }

    /**
     * Get the page URL for current locale
     *
     * @return string
     */
    public function getUrlAttribute(): string
    {
        return localizedRoute('page.show', $this->slug);
    }

    # https://stackoverflow.com/questions/59802926/laravel-cache-with-route-model-binding
    // public function resolveRouteBinding($value, $field = null): ?Model
    // {
    //     return Cache::rememberForever(
    //         cacheKey($value),
    //         fn () => $this->where('slug', $value)->firstOrFail()
    //     );
    // }
}
