<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pages', function (Blueprint $table) {
            // Language field
            $table->string('locale', 5)->default('en')->after('id');
            
            // Add indexes for better performance
            $table->index('locale');
            $table->index(['locale', 'published_at']);
            $table->index(['locale', 'location']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pages', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['locale', 'location']);
            $table->dropIndex(['locale', 'published_at']);
            $table->dropIndex(['locale']);
            
            // Drop column
            $table->dropColumn('locale');
        });
    }
};
