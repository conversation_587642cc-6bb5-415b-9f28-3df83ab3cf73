<?php

namespace App\Models;

use Cyrilde<PERSON>it\EloquentViewable\Contracts\Viewable;
use CyrildeWit\EloquentViewable\InteractsWithViews;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Post extends Model implements Viewable
{
    use HasFactory, InteractsWithViews;

    protected $removeViewsOnDelete = true;

    protected $casts = [
        'published_at' => 'date',
        'tags' => 'array',
        'related_posts' => 'array',
    ];

    protected $fillable = [
        'title',
        'slug',
        'topAd',
        'bottomAd',
        'showShareButtons',
        'content',
        'metaDescription',
        'metaKeywords',
        'snippet',
        'thumbnail',
        'published_at',
        'locale', // Language code (en, ar)
        'meta_title', // SEO title
        'excerpt', // Short description
        'featured', // Featured post flag
        'reading_time', // Estimated reading time
        'author_name', // Author name
        'tags', // Post tags (JSON)
        'related_posts', // Related posts (JSON)
    ];

    /**
     * Scope to filter posts by locale
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $locale
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForLocale($query, $locale = null)
    {
        $locale = $locale ?: app()->getLocale();
        return $query->where('locale', $locale);
    }

    /**
     * Scope to get published posts
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePublished($query)
    {
        return $query->whereNotNull('published_at')
                    ->where('published_at', '<=', now());
    }

    /**
     * Scope to get featured posts
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    /**
     * Get the post's SEO title or fallback to title
     *
     * @return string
     */
    public function getSeoTitleAttribute(): string
    {
        return $this->meta_title ?: $this->title;
    }

    /**
     * Get the post's excerpt or generate from content
     *
     * @return string
     */
    public function getExcerptAttribute(): string
    {
        if ($this->attributes['excerpt']) {
            return $this->attributes['excerpt'];
        }

        // Generate excerpt from content
        $content = strip_tags($this->content);
        return substr($content, 0, 160) . '...';
    }

    /**
     * Get estimated reading time
     *
     * @return int
     */
    public function getReadingTimeAttribute(): int
    {
        if ($this->attributes['reading_time']) {
            return $this->attributes['reading_time'];
        }

        // Calculate reading time (average 200 words per minute)
        $wordCount = str_word_count(strip_tags($this->content));
        return max(1, ceil($wordCount / 200));
    }

    /**
     * Get the post URL for current locale
     *
     * @return string
     */
    public function getUrlAttribute(): string
    {
        return localizedRoute('blog.show', $this->slug);
    }

    /**
     * Get related posts in the same language
     *
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getRelatedPosts($limit = 3)
    {
        if ($this->related_posts) {
            return static::whereIn('id', $this->related_posts)
                        ->forLocale($this->locale)
                        ->published()
                        ->limit($limit)
                        ->get();
        }

        // Auto-generate related posts based on tags or recent posts
        return static::where('id', '!=', $this->id)
                    ->forLocale($this->locale)
                    ->published()
                    ->latest('published_at')
                    ->limit($limit)
                    ->get();
    }

     # https://stackoverflow.com/questions/59802926/laravel-cache-with-route-model-binding
    //  public function resolveRouteBinding($value, $field = null): ?Model
    //  {
    //     return Cache::rememberForever(
    //         cacheKey($value),
    //         fn () => $this->where('slug', $value)->firstOrFail()
    //     );
    //  }
}

