<?php

use App\Http\Controllers\BlogPageController;
use App\Http\Controllers\ContactPageController;
use App\Http\Controllers\ConvertersController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\ResolvePathController;
use App\Livewire\Installer;
use App\Livewire\Uploader;
use Illuminate\Support\Facades\Route;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

# must come first other wise /{path} will be executed
Route::get('/install', Installer::class)->name('installer');

// Enhanced Sitemap Routes (outside localization group for better SEO)
Route::get('/sitemap.xml', [\App\Http\Controllers\SitemapController::class, 'index'])->name('sitemap.index');
Route::get('/sitemap-main.xml', [\App\Http\Controllers\SitemapController::class, 'main'])->name('sitemap.main');
Route::get('/sitemap-converters.xml', [\App\Http\Controllers\SitemapController::class, 'converters'])->name('sitemap.converters');
Route::get('/sitemap-posts.xml', [\App\Http\Controllers\SitemapController::class, 'posts'])->name('sitemap.posts');
Route::get('/sitemap-pages.xml', [\App\Http\Controllers\SitemapController::class, 'pages'])->name('sitemap.pages');

// Robots.txt route
Route::get('/robots.txt', function () {
    $content = "User-agent: *\n";
    $content .= "Allow: /\n";
    $content .= "Disallow: /admin/\n";
    $content .= "Disallow: /storage/uploads/\n";
    $content .= "Disallow: /livewire/\n\n";
    $content .= "Sitemap: " . url('/sitemap.xml') . "\n";

    return response($content, 200, [
        'Content-Type' => 'text/plain',
        'Cache-Control' => 'public, max-age=86400', // 24 hours
    ]);
})->name('robots');
Route::group([
    'prefix' => LaravelLocalization::setLocale(),
    'middleware' => [
        'installer',
        'localeSessionRedirect',
        'localizationRedirect',
        'localeViewPath'
    ]
], function () {
    Route::get('/', [HomeController::class, 'index'])->name('home');
    Route::get('/contact', [ContactPageController::class, 'index'])->name('contact');
    Route::get('/blog', [BlogPageController::class, 'index'])->name('blog');
    Route::post('/upload', [Uploader::class, 'save'])->name('upload');
    Route::get('/download', [Uploader::class, 'download'])->name('download');
    Route::get('/download-zip', [Uploader::class, 'downloadZip'])->name('downloadZip');
    Route::get('/{path}', [ResolvePathController::class, 'resolve'])->name('resolver');
});