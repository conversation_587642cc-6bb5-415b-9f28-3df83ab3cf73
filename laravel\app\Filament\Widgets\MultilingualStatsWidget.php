<?php

namespace App\Filament\Widgets;

use App\Models\Post;
use App\Models\Page;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

/**
 * Multilingual Statistics Widget
 * 
 * Shows content statistics for each language
 */
class MultilingualStatsWidget extends BaseWidget
{
    protected static ?string $pollingInterval = '30s';
    protected static bool $isLazy = false;

    protected function getStats(): array
    {
        // Get content counts for each language
        $englishPosts = Post::forLocale('en')->published()->count();
        $arabicPosts = Post::forLocale('ar')->published()->count();
        $englishPages = Page::forLocale('en')->published()->count();
        $arabicPages = Page::forLocale('ar')->published()->count();
        
        // Get total views for each language
        $englishPostViews = Post::forLocale('en')->published()->get()->sum(function ($post) {
            return views($post)->count();
        });
        
        $arabicPostViews = Post::forLocale('ar')->published()->get()->sum(function ($post) {
            return views($post)->count();
        });

        return [
            // English Content Stats
            Stat::make('🇺🇸 English Posts', $englishPosts)
                ->description('Published English articles')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('primary')
                ->chart([7, 2, 10, 3, 15, 4, 17])
                ->extraAttributes([
                    'class' => 'cursor-pointer',
                    'wire:click' => '$dispatch("filterByLanguage", { language: "en", type: "posts" })',
                ]),

            Stat::make('🇸🇦 Arabic Posts', $arabicPosts)
                ->description('Published Arabic articles')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('success')
                ->chart([4, 8, 6, 12, 9, 14, 11])
                ->extraAttributes([
                    'class' => 'cursor-pointer',
                    'wire:click' => '$dispatch("filterByLanguage", { language: "ar", type: "posts" })',
                ]),

            // Pages Stats
            Stat::make('📄 English Pages', $englishPages)
                ->description('Published English pages')
                ->descriptionIcon('heroicon-m-document')
                ->color('warning')
                ->extraAttributes([
                    'class' => 'cursor-pointer',
                    'wire:click' => '$dispatch("filterByLanguage", { language: "en", type: "pages" })',
                ]),

            Stat::make('📄 Arabic Pages', $arabicPages)
                ->description('Published Arabic pages')
                ->descriptionIcon('heroicon-m-document')
                ->color('info')
                ->extraAttributes([
                    'class' => 'cursor-pointer',
                    'wire:click' => '$dispatch("filterByLanguage", { language: "ar", type: "pages" })',
                ]),

            // Views Stats
            Stat::make('👁️ English Views', number_format($englishPostViews))
                ->description('Total views on English content')
                ->descriptionIcon('heroicon-m-eye')
                ->color('primary')
                ->chart([100, 150, 200, 180, 220, 250, 300]),

            Stat::make('👁️ Arabic Views', number_format($arabicPostViews))
                ->description('Total views on Arabic content')
                ->descriptionIcon('heroicon-m-eye')
                ->color('success')
                ->chart([80, 120, 160, 140, 180, 200, 240]),
        ];
    }

    protected function getColumns(): int
    {
        return 3; // Display 3 stats per row
    }
}
