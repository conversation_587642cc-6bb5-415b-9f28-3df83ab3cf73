<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * Performance Optimization Middleware
 * 
 * This middleware handles various performance optimizations including:
 * - HTTP headers for caching and security
 * - Compression
 * - Security headers
 * - Performance monitoring
 */
class PerformanceOptimization
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $startTime = microtime(true);
        
        // Process the request
        $response = $next($request);
        
        // Apply optimizations to the response
        $this->optimizeResponse($request, $response);
        
        // Log performance metrics
        $this->logPerformanceMetrics($request, $startTime);
        
        return $response;
    }

    /**
     * Optimize the HTTP response
     *
     * @param Request $request
     * @param Response $response
     * @return void
     */
    private function optimizeResponse(Request $request, $response): void
    {
        // Add security headers
        $this->addSecurityHeaders($response);
        
        // Add caching headers
        $this->addCachingHeaders($request, $response);
        
        // Add performance headers
        $this->addPerformanceHeaders($response);
        
        // Compress response if applicable
        $this->compressResponse($request, $response);
        
        // Add CORS headers if needed
        $this->addCorsHeaders($response);
    }

    /**
     * Add security headers to the response
     *
     * @param Response $response
     * @return void
     */
    private function addSecurityHeaders($response): void
    {
        $headers = [
            // Prevent clickjacking
            'X-Frame-Options' => 'SAMEORIGIN',
            
            // Prevent MIME type sniffing
            'X-Content-Type-Options' => 'nosniff',
            
            // XSS Protection
            'X-XSS-Protection' => '1; mode=block',
            
            // Referrer Policy
            'Referrer-Policy' => 'strict-origin-when-cross-origin',
            
            // Content Security Policy (basic)
            'Content-Security-Policy' => "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: https:; connect-src 'self' https://www.google-analytics.com;",
            
            // Permissions Policy
            'Permissions-Policy' => 'camera=(), microphone=(), geolocation=()',
            
            // Remove server information
            'Server' => '',
        ];

        foreach ($headers as $key => $value) {
            $response->headers->set($key, $value);
        }
    }

    /**
     * Add caching headers based on content type and route
     *
     * @param Request $request
     * @param Response $response
     * @return void
     */
    private function addCachingHeaders(Request $request, $response): void
    {
        $path = $request->path();
        $contentType = $response->headers->get('Content-Type', '');
        
        // Static assets - long cache
        if ($this->isStaticAsset($path)) {
            $response->headers->set('Cache-Control', 'public, max-age=31536000, immutable'); // 1 year
            $response->headers->set('Expires', gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');
        }
        // Images - medium cache
        elseif ($this->isImage($path)) {
            $response->headers->set('Cache-Control', 'public, max-age=2592000'); // 30 days
            $response->headers->set('Expires', gmdate('D, d M Y H:i:s', time() + 2592000) . ' GMT');
        }
        // API responses - short cache
        elseif (str_starts_with($path, 'api/')) {
            $response->headers->set('Cache-Control', 'public, max-age=300'); // 5 minutes
        }
        // HTML pages - very short cache
        elseif (str_contains($contentType, 'text/html')) {
            $response->headers->set('Cache-Control', 'public, max-age=60'); // 1 minute
        }
        // Default - no cache for dynamic content
        else {
            $response->headers->set('Cache-Control', 'no-cache, no-store, must-revalidate');
            $response->headers->set('Pragma', 'no-cache');
            $response->headers->set('Expires', '0');
        }
        
        // Add ETag for better caching
        if ($response->getContent()) {
            $etag = md5($response->getContent());
            $response->headers->set('ETag', '"' . $etag . '"');
            
            // Check if client has cached version
            if ($request->header('If-None-Match') === '"' . $etag . '"') {
                $response->setStatusCode(304);
                $response->setContent('');
            }
        }
    }

    /**
     * Add performance-related headers
     *
     * @param Response $response
     * @return void
     */
    private function addPerformanceHeaders($response): void
    {
        // DNS prefetch hints
        $response->headers->set('Link', '<https://fonts.googleapis.com>; rel=dns-prefetch, <https://cdnjs.cloudflare.com>; rel=dns-prefetch');
        
        // Vary header for better caching
        $response->headers->set('Vary', 'Accept-Encoding, Accept-Language');
    }

    /**
     * Compress response content if supported by client
     *
     * @param Request $request
     * @param Response $response
     * @return void
     */
    private function compressResponse(Request $request, $response): void
    {
        $acceptEncoding = $request->header('Accept-Encoding', '');
        $content = $response->getContent();
        
        // Only compress if content is large enough and client supports it
        if (strlen($content) > 1024 && !empty($content)) {
            if (str_contains($acceptEncoding, 'gzip') && function_exists('gzencode')) {
                $compressed = gzencode($content, 6); // Compression level 6 (good balance)
                if ($compressed !== false) {
                    $response->setContent($compressed);
                    $response->headers->set('Content-Encoding', 'gzip');
                    $response->headers->set('Content-Length', strlen($compressed));
                }
            }
        }
    }

    /**
     * Add CORS headers if needed
     *
     * @param Response $response
     * @return void
     */
    private function addCorsHeaders($response): void
    {
        $response->headers->set('Access-Control-Allow-Origin', '*');
        $response->headers->set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        $response->headers->set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
        $response->headers->set('Access-Control-Max-Age', '86400'); // 24 hours
    }

    /**
     * Log performance metrics for monitoring
     *
     * @param Request $request
     * @param float $startTime
     * @return void
     */
    private function logPerformanceMetrics(Request $request, float $startTime): void
    {
        $executionTime = (microtime(true) - $startTime) * 1000; // Convert to milliseconds
        $memoryUsage = memory_get_peak_usage(true);
        
        // Log slow requests
        if ($executionTime > 1000) { // Requests taking more than 1 second
            Log::warning('Slow request detected', [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'execution_time' => $executionTime . 'ms',
                'memory_usage' => formatFileSize($memoryUsage),
                'user_agent' => $request->userAgent(),
                'ip' => $request->ip(),
            ]);
        }
        
        // Store metrics in cache for dashboard
        $cacheKey = 'performance_metrics_' . date('Y-m-d-H');
        $metrics = Cache::get($cacheKey, []);
        
        $metrics[] = [
            'timestamp' => time(),
            'execution_time' => $executionTime,
            'memory_usage' => $memoryUsage,
            'path' => $request->path(),
            'method' => $request->method(),
        ];
        
        // Keep only last 100 requests per hour
        if (count($metrics) > 100) {
            $metrics = array_slice($metrics, -100);
        }
        
        Cache::put($cacheKey, $metrics, 3600); // Store for 1 hour
    }

    /**
     * Check if the path is a static asset
     *
     * @param string $path
     * @return bool
     */
    private function isStaticAsset(string $path): bool
    {
        return preg_match('/\.(css|js|woff|woff2|ttf|eot|otf)$/i', $path);
    }

    /**
     * Check if the path is an image
     *
     * @param string $path
     * @return bool
     */
    private function isImage(string $path): bool
    {
        return preg_match('/\.(jpg|jpeg|png|gif|webp|svg|ico|bmp|tiff|heic|heif|avif)$/i', $path);
    }
}
