<?php

namespace App\Http\Controllers;

use App\Settings\ConvertersSlugsSettings;
use App\Settings\HomePageSettings;
use Artesaos\SEOTools\Facades\SEOTools;
use Artesaos\SEOTools\Facades\OpenGraph;
use Artesaos\SEOTools\Facades\TwitterCard;
use Artesaos\SEOTools\Facades\JsonLd;

class HomeController extends Controller
{
    /**
     * Display the home page with enhanced SEO optimization
     *
     * @param HomePageSettings $settings
     * @param ConvertersSlugsSettings $slugs
     * @return \Illuminate\View\View
     */
    public function index(
        HomePageSettings $settings,
        ConvertersSlugsSettings $slugs
    ) {
        // Enhanced SEO meta tags
        SEOTools::webPage(
            title: $settings->title,
            description: $settings->metaDescription,
            keyWords: $settings->metaKeywords,
        );

        // Open Graph tags for social media sharing
        OpenGraph::setTitle($settings->title)
            ->setDescription($settings->metaDescription)
            ->setUrl(url('/'))
            ->setType('website')
            ->setSiteName(config('app.name'))
            ->addImage(asset('storage/logo.png'), [
                'height' => 630,
                'width' => 1200,
                'alt' => $settings->title
            ]);

        // Twitter Card optimization
        TwitterCard::setType('summary_large_image')
            ->setTitle($settings->title)
            ->setDescription($settings->metaDescription)
            ->setImage(asset('storage/logo.png'))
            ->setSite('@' . config('app.name'));

        // JSON-LD structured data for better search engine understanding
        JsonLd::setTitle($settings->title)
            ->setDescription($settings->metaDescription)
            ->setType('WebApplication')
            ->addValue('applicationCategory', 'UtilityApplication')
            ->addValue('operatingSystem', 'Web Browser')
            ->addValue('url', url('/'))
            ->addValue('offers', [
                '@type' => 'Offer',
                'price' => '0',
                'priceCurrency' => 'USD'
            ]);

        // Add breadcrumb structured data
        JsonLd::addValue('breadcrumb', [
            '@type' => 'BreadcrumbList',
            'itemListElement' => [
                [
                    '@type' => 'ListItem',
                    'position' => 1,
                    'name' => __('Home'),
                    'item' => url('/')
                ]
            ]
        ]);

        return view('pages.home', [
            'settings' => $settings,
            'slugs' => $slugs,
        ]);
    }
}
