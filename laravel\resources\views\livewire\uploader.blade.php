<section x-data="filepond" class="mb-8 uploader" x-ref="result" role="main" aria-labelledby="uploader-heading">
    {{-- Screen reader heading --}}
    <h2 id="uploader-heading" class="sr-only">{{ __('Image Upload and Conversion Tool') }}</h2>

    {{-- Loading skeleton with improved accessibility --}}
    <div x-show="showPlaceHolder" class="flex flex-col w-full gap-4" role="status" aria-label="{{ __('Loading uploader') }}">
        <div class="w-full h-60 skeleton animate-pulse bg-gray-200 rounded-lg" aria-hidden="true"></div>
        <div class="h-12 rounded-md skeleton w-28 animate-pulse bg-gray-200" aria-hidden="true"></div>
        <span class="sr-only">{{ __('Loading...') }}</span>
    </div>

    @if ($showUploader)
        <div x-show='showUploader' class="space-y-4">
            {{-- File upload area with improved accessibility --}}
            <div wire:ignore class="upload-container">
                <div class="filepond" x-ref='filepond'
                     role="application"
                     aria-label="{{ __('Drag and drop images here or click to browse') }}"
                     tabindex="0"></div>
            </div>

            {{-- Convert button with enhanced UX --}}
            <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
                <button x-cloak
                        x-show="showConvertBtn"
                        x-bind:disabled="isConvertBtnDisabled"
                        @click="convert"
                        class="flex items-center justify-center px-6 py-3 text-base font-medium leading-4 convert-btn bg-convert-btn-bg-color hover:bg-convert-btn-bg-color text-convert-btn-text-color rounded-lg shadow-md hover:shadow-lg transition-all duration-300 opacity-95 hover:opacity-100 disabled:text-convert-btn-text-color disabled:bg-convert-btn-bg-color disabled:opacity-60 disabled:cursor-not-allowed focus:outline-none focus:ring-4 focus:ring-blue-300 focus:ring-opacity-50"
                        aria-describedby="convert-help">
                    <x-icons.chevron-right class="w-5 h-5 {{ currentLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" stroke-width="2.5" />
                    {{ __('Convert') }}
                </button>

                {{-- Help text --}}
                <p id="convert-help" class="text-sm text-gray-600" x-show="showConvertBtn">
                    {{ __('Click to start converting your uploaded images') }}
                </p>
            </div>
        </div>
    @endif
    {{-- Loading overlay with improved accessibility --}}
    <div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm"
         wire:loading.flex wire:target="convert"
         role="dialog"
         aria-modal="true"
         aria-labelledby="loading-title">
        <div class="bg-white rounded-lg p-8 shadow-xl max-w-sm mx-4 text-center">
            <x-icons.spinner class="w-16 h-16 mx-auto animate-spin text-blue-600 mb-4" stroke-width="2" />
            <h3 id="loading-title" class="text-lg font-semibold text-gray-900 mb-2">
                {{ __('Converting Images') }}
            </h3>
            <p class="text-gray-600">
                {{ __('Please wait while we process your images...') }}
            </p>
        </div>
    </div>

    {{-- Error messages with improved accessibility --}}
    @if ($errors->any())
        <div class="flex flex-col gap-3 mb-6" role="alert" aria-labelledby="error-heading">
            <h3 id="error-heading" class="sr-only">{{ __('Conversion Errors') }}</h3>
            @foreach ($errors->all() as $error)
                <div class="flex items-start gap-3 p-4 text-red-800 bg-red-100 border border-red-200 rounded-lg error-box">
                    <x-icons.alert class="h-5 w-5 mt-0.5 flex-shrink-0" stroke-width="2.25" />
                    <span class="flex-1 text-sm leading-relaxed">{{ $error }}</span>
                </div>
            @endforeach
        </div>

        @if (empty($results))
            <div class="text-center">
                <a class="inline-flex items-center px-6 py-3 text-base font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors duration-300 focus:outline-none focus:ring-4 focus:ring-blue-300 focus:ring-opacity-50"
                   href="{{ localizedRoute('resolver', $slug) }}">
                    <x-icons.refresh class="w-5 h-5 {{ currentLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" stroke-width="2" />
                    {{ __('Try other images') }}
                </a>
            </div>
        @endif
    @endif
    {{-- Results section with improved accessibility and design --}}
    @if (!empty($results))
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 result" role="region" aria-labelledby="results-heading">
            <div class="p-6 border-b border-gray-200">
                <h3 id="results-heading" class="text-lg font-semibold text-gray-900 flex items-center">
                    <x-icons.check-circle class="w-6 h-6 text-green-600 {{ currentLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" stroke-width="2" />
                    {{ __('Conversion Complete') }}
                </h3>
                <p class="text-sm text-gray-600 mt-1">
                    {{ __(':count images successfully converted to :format format', [
                        'count' => count($results['images']),
                        'format' => strtoupper($format)
                    ]) }}
                </p>
            </div>

            <div class="p-6 space-y-4">
                @foreach ($results['images'] as $index => $result)
                    @php
                        $id = $result['id'];
                        $basename = $result['basename'];
                        $size = $result['size'];
                    @endphp
                    <div class="flex items-center p-4 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors duration-200">
                        {{-- File icon --}}
                        <div class="flex-shrink-0 {{ currentLocale() === 'ar' ? 'ml-4' : 'mr-4' }}">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <x-icons.image class="w-6 h-6 text-blue-600" stroke-width="2" />
                            </div>
                        </div>

                        {{-- File info --}}
                        <div class="flex-1 min-w-0">
                            <h4 class="text-sm font-medium text-gray-900 truncate" title="{{ $basename }}.{{ $format }}">
                                {{ $basename }}.{{ $format }}
                            </h4>
                            <p class="text-sm text-gray-500">
                                {{ __('Size: :size', ['size' => $size]) }}
                            </p>
                        </div>

                        {{-- Download button --}}
                        <div class="flex-shrink-0 {{ currentLocale() === 'ar' ? 'mr-4' : 'ml-4' }}">
                            <a href="{{ localizedRoute('download') }}/?id={{ $id }}&basename={{ $basename }}&format={{ $format }}"
                               class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-4 focus:ring-blue-300 focus:ring-opacity-50"
                               target="_blank"
                               aria-label="{{ __('Download :filename', ['filename' => $basename . '.' . $format]) }}">
                                <x-icons.download class="w-4 h-4 {{ currentLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" stroke-width="2" />
                                {{ __('Download') }}
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
            {{-- Action buttons footer --}}
            <div class="p-6 bg-gray-50 border-t border-gray-200 rounded-b-xl">
                <div class="flex flex-col sm:flex-row gap-4 items-center justify-between">
                    {{-- Left side actions --}}
                    <div class="flex flex-col sm:flex-row gap-3">
                        <a class="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-white border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors duration-200 focus:outline-none focus:ring-4 focus:ring-blue-300 focus:ring-opacity-50"
                           href="{{ localizedRoute('resolver', $slug) }}">
                            <x-icons.plus class="w-4 h-4 {{ currentLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" stroke-width="2" />
                            {{ __('Convert More!') }}
                        </a>

                        <button onclick="deleteModal.showModal()"
                                class="inline-flex items-center px-4 py-2 text-sm font-medium text-red-600 bg-white border border-red-600 rounded-lg hover:bg-red-50 transition-colors duration-200 focus:outline-none focus:ring-4 focus:ring-red-300 focus:ring-opacity-50"
                                aria-label="{{ __('Delete all converted images') }}">
                            <x-icons.trash class="w-4 h-4 {{ currentLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" stroke-width="2" />
                            {{ __('Delete All') }}
                        </button>
                    </div>

                    {{-- Right side - ZIP download --}}
                    <div>
                        @if ($results['zipId'] == null)
                            <button disabled
                                    class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-400 bg-gray-200 border border-gray-300 rounded-lg cursor-not-allowed"
                                    aria-label="{{ __('ZIP download unavailable') }}">
                                <x-icons.download class="w-4 h-4 {{ currentLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" stroke-width="2" />
                                {{ __('ZIP Unavailable') }}
                            </button>
                        @else
                            <a href="{{ localizedRoute('downloadZip') }}/?zipId={{ $results['zipId'] }}"
                               class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 transition-colors duration-200 focus:outline-none focus:ring-4 focus:ring-green-300 focus:ring-opacity-50"
                               target="_blank"
                               aria-label="{{ __('Download all images as ZIP file') }}">
                                <x-icons.download class="w-4 h-4 {{ currentLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" stroke-width="2" />
                                {{ __('Download ZIP') }}
                            </a>
                        @endif
                    </div>
                </div>
            </div>

            {{-- Delete confirmation modal with improved accessibility --}}
            <dialog id="deleteModal" class="modal backdrop:bg-black backdrop:bg-opacity-50">
                <div class="modal-box bg-white rounded-xl shadow-xl max-w-md">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                        <x-icons.alert-triangle class="w-6 h-6 text-red-600 {{ currentLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" stroke-width="2" />
                        {{ __('Confirm Deletion') }}
                    </h3>

                    <p class="text-gray-600 mb-6 leading-relaxed">
                        {{ __('Are you sure you want to delete all converted images from the server? This action cannot be undone.') }}
                    </p>

                    <div class="flex flex-col sm:flex-row gap-3 justify-end">
                        <button onclick="deleteModal.close()"
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors duration-200 focus:outline-none focus:ring-4 focus:ring-gray-300 focus:ring-opacity-50">
                            {{ __('Cancel') }}
                        </button>

                        <a wire:click="deleteAll"
                           onclick="deleteModal.close()"
                           class="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 transition-colors duration-200 focus:outline-none focus:ring-4 focus:ring-red-300 focus:ring-opacity-50"
                           href="{{ localizedRoute('resolver', $slug) }}">
                            {{ __('Delete All') }}
                        </a>
                    </div>
                </div>
            </dialog>
        </div>
    @endif
</section>
