{"name": "image-converter", "version": "2.0.0", "description": "Advanced Image Converter with Enhanced Performance and SEO", "keywords": ["image converter", "laravel", "filament", "livewire", "alpine.js", "filepond", "image processing", "seo optimized"], "author": "Image Converter Team", "license": "MIT", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "build:production": "NODE_ENV=production vite build", "build:analyze": "vite build --mode analyze", "lint": "eslint resources/js --ext .js,.vue --fix", "lint:css": "stylelint resources/css/**/*.css --fix", "format": "prettier --write resources/js/**/*.js resources/css/**/*.css", "test": "jest", "test:watch": "jest --watch", "clean": "rm -rf public/build node_modules/.vite", "postinstall": "npm run build:production"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@vitejs/plugin-vue": "^4.5.2", "autoprefixer": "^10.4.16", "axios": "^1.6.2", "cssnano": "^6.0.2", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.2", "jest": "^29.7.0", "laravel-vite-plugin": "^1.0.0", "lodash": "^4.17.21", "postcss": "^8.4.32", "prettier": "^3.1.1", "stylelint": "^16.1.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-standard": "^36.0.0", "tailwindcss": "^3.4.0", "terser": "^5.26.0", "vite": "^5.0.10"}, "dependencies": {"alpinejs": "^3.13.3", "filepond": "^4.30.4", "filepond-plugin-file-validate-size": "^2.2.8", "filepond-plugin-file-validate-type": "^1.2.8", "filepond-plugin-image-exif-orientation": "^1.0.11", "filepond-plugin-image-preview": "^4.6.12", "intersection-observer": "^0.12.2", "web-vitals": "^3.5.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/image-converter.git"}, "bugs": {"url": "https://github.com/your-username/image-converter/issues"}, "homepage": "https://github.com/your-username/image-converter#readme", "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/tests/js/setup.js"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/resources/$1", "^@js/(.*)$": "<rootDir>/resources/js/$1", "^@css/(.*)$": "<rootDir>/resources/css/$1"}, "testMatch": ["<rootDir>/tests/js/**/*.test.js"], "collectCoverageFrom": ["resources/js/**/*.js", "!resources/js/bootstrap.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "eslintConfig": {"env": {"browser": true, "es2021": true, "node": true}, "extends": ["eslint:recommended", "prettier"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "rules": {"no-console": "warn", "no-debugger": "error", "no-unused-vars": "warn", "prefer-const": "error", "no-var": "error"}, "globals": {"Alpine": "readonly", "FilePond": "readonly", "Livewire": "readonly", "gtag": "readonly"}}, "stylelint": {"extends": ["stylelint-config-standard", "stylelint-config-prettier"], "rules": {"at-rule-no-unknown": [true, {"ignoreAtRules": ["tailwind", "apply", "variants", "responsive", "screen"]}], "declaration-block-trailing-semicolon": null, "no-descending-specificity": null}}, "prettier": {"semi": true, "singleQuote": true, "tabWidth": 4, "trailingComma": "es5", "printWidth": 100, "bracketSpacing": true, "arrowParens": "avoid"}}