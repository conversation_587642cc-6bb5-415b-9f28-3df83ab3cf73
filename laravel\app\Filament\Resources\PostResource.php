<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PostResource\Pages;
use Illuminate\Support\Str;
use App\Models\Post;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Card;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Mohamedsabil83\FilamentFormsTinyeditor\Components\TinyEditor;

class PostResource extends Resource
{
    protected static ?string $model = Post::class;
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationLabel = 'Blog Posts';
    protected static ?string $navigationGroup = 'Content';
    protected static ?int $navigationSort = 4;

    public static $toSanitize = [
        'title',
        'slug',
        'metaDescription',
        'metaKeywords',
        'snippet',
    ];

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Language and Basic Info Section
                Section::make('📝 Basic Information')
                    ->description('Set the language and basic details for your post')
                    ->schema([
                        Grid::make(3)->schema([
                            Select::make('locale')
                                ->label('🌍 Language')
                                ->options([
                                    'en' => '🇺🇸 English',
                                    'ar' => '🇸🇦 العربية (Arabic)',
                                ])
                                ->default('en')
                                ->required()
                                ->reactive()
                                ->helperText('Choose the language for this post'),

                            Toggle::make('featured')
                                ->label('⭐ Featured Post')
                                ->helperText('Mark as featured to highlight this post')
                                ->default(false),

                            DatePicker::make('published_at')
                                ->label('📅 Published Date')
                                ->default(now())
                                ->helperText('When should this post be published?')
                        ]),

                        Grid::make(2)->schema([
                            TextInput::make('title')
                                ->label('📰 Post Title')
                                ->reactive()
                                ->placeholder('Enter an engaging title for your post')
                                ->afterStateUpdated(function ($state, $set, $get) {
                                    $locale = $get('locale') ?? 'en';
                                    if ($locale === 'ar') {
                                        // For Arabic, keep original characters
                                        $set('slug', $state);
                                    } else {
                                        $set('slug', Str::slug($state));
                                    }
                                })
                                ->required()
                                ->maxLength(100)
                                ->helperText('Max 100 characters - This will be the main heading'),

                            TextInput::make('slug')
                                ->label('🔗 URL Slug')
                                ->placeholder('post-url-slug')
                                ->required()
                                ->maxLength(100)
                                ->helperText('URL-friendly version of the title')
                                ->unique(Post::class, 'slug', ignoreRecord: true),
                        ]),

                        TextInput::make('author_name')
                            ->label('✍️ Author Name')
                            ->placeholder('Author name (optional)')
                            ->maxLength(100)
                            ->helperText('Leave empty to use site name'),
                    ]),

                // SEO Section
                Section::make('🔍 SEO Optimization')
                    ->description('Optimize your post for search engines')
                    ->schema([
                        TextInput::make('meta_title')
                            ->label('📊 SEO Title')
                            ->placeholder('Custom SEO title (optional)')
                            ->maxLength(60)
                            ->helperText('Leave empty to use post title. Max 60 characters for best SEO'),

                        Textarea::make('metaDescription')
                            ->label('📝 Meta Description')
                            ->rows(3)
                            ->placeholder('Brief description for search engines')
                            ->maxLength(160)
                            ->helperText('Max 160 characters - This appears in search results')
                            ->required(),

                        Textarea::make('excerpt')
                            ->label('📄 Post Excerpt')
                            ->rows(3)
                            ->placeholder('Short summary of the post content')
                            ->maxLength(300)
                            ->helperText('Used in post previews and social sharing'),

                        TagsInput::make('tags')
                            ->label('🏷️ Tags')
                            ->placeholder('Add relevant tags')
                            ->helperText('Press Enter after each tag. Helps with categorization and SEO'),

                        Textarea::make('metaKeywords')
                            ->label('🔑 Meta Keywords')
                            ->placeholder('keyword1, keyword2, keyword3')
                            ->helperText('Comma-separated keywords (optional)')
                            ->rows(2),
                    ]),

                // Content Section
                Section::make('📝 Content')
                    ->description('Write your post content and add media')
                    ->schema([
                        FileUpload::make('thumbnail')
                            ->label('🖼️ Featured Image')
                            ->image()
                            ->maxSize(2048)
                            ->directory('images')
                            ->helperText('1200x630 recommended for social sharing. Max 2MB')
                            ->imageEditor()
                            ->imageEditorAspectRatios([
                                '16:9',
                                '4:3',
                                '1:1',
                            ]),

                        TinyEditor::make('content')
                            ->label('📄 Post Content')
                            ->profile('custom')
                            ->fileAttachmentsDisk('public_storage')
                            ->required()
                            ->helperText('Write your full post content here'),

                        Textarea::make('snippet')
                            ->label('📋 Post Snippet')
                            ->rows(3)
                            ->placeholder('Short preview text for blog listing page')
                            ->required()
                            ->maxLength(200)
                            ->helperText('Used in blog listing page. Max 200 characters'),
                    ]),

                // Display Settings Section
                Section::make('⚙️ Display Settings')
                    ->description('Control how this post appears on your site')
                    ->schema([
                        Grid::make(3)->schema([
                            Toggle::make('topAd')
                                ->label('📢 Show Top Ad')
                                ->default(true)
                                ->helperText('Display advertisement at the top'),

                            Toggle::make('bottomAd')
                                ->label('📢 Show Bottom Ad')
                                ->default(true)
                                ->helperText('Display advertisement at the bottom'),

                            Toggle::make('showShareButtons')
                                ->label('📤 Show Share Buttons')
                                ->default(true)
                                ->helperText('Enable social media sharing'),
                        ]),

                        Select::make('related_posts')
                            ->label('🔗 Related Posts')
                            ->multiple()
                            ->searchable()
                            ->options(function ($get) {
                                $currentLocale = $get('locale') ?? 'en';
                                return Post::where('locale', $currentLocale)
                                         ->pluck('title', 'id')
                                         ->toArray();
                            })
                            ->helperText('Select related posts in the same language'),

                        TextInput::make('reading_time')
                            ->label('⏱️ Reading Time (minutes)')
                            ->numeric()
                            ->placeholder('Auto-calculated if left empty')
                            ->helperText('Estimated reading time in minutes'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('title')
                    ->label('📰 Title')
                    ->searchable()
                    ->sortable()
                    ->limit(50)
                    ->tooltip(fn ($record) => $record->title),

                TextColumn::make('locale')
                    ->label('🌍 Language')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'en' => '🇺🇸 English',
                        'ar' => '🇸🇦 العربية',
                        default => $state,
                    })
                    ->colors([
                        'primary' => 'en',
                        'success' => 'ar',
                    ])
                    ->sortable(),

                TextColumn::make('status')
                    ->label('📊 Status')
                    ->badge()
                    ->getStateUsing(function ($record): string {
                        if (!$record->published_at) return 'Draft';
                        return $record->published_at->isPast() ? 'Published' : 'Scheduled';
                    })
                    ->colors([
                        'success' => 'Published',
                        'warning' => 'Scheduled',
                        'gray' => 'Draft',
                    ]),

                TextColumn::make('featured')
                    ->label('⭐ Featured')
                    ->badge()
                    ->formatStateUsing(fn (bool $state): string => $state ? 'Yes' : 'No')
                    ->colors([
                        'success' => fn ($state) => $state === true,
                        'gray' => fn ($state) => $state === false,
                    ]),

                TextColumn::make('author_name')
                    ->label('✍️ Author')
                    ->default('Site Admin')
                    ->limit(20),

                TextColumn::make('published_at')
                    ->label('📅 Published')
                    ->sortable()
                    ->date('M j, Y')
                    ->placeholder('Not published'),

                TextColumn::make('views_count')
                    ->label('👁️ Views')
                    ->getStateUsing(fn ($record) => number_format(views($record)->count()))
                    ->sortable(),

                TextColumn::make('reading_time')
                    ->label('⏱️ Read Time')
                    ->formatStateUsing(fn ($state) => $state ? $state . ' min' : 'Auto')
                    ->alignCenter(),
            ])
            ->filters([
                \Filament\Tables\Filters\SelectFilter::make('locale')
                    ->label('Language')
                    ->options([
                        'en' => '🇺🇸 English',
                        'ar' => '🇸🇦 العربية',
                    ]),

                \Filament\Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'published' => 'Published',
                        'draft' => 'Draft',
                        'scheduled' => 'Scheduled',
                    ])
                    ->query(function ($query, $data) {
                        return match ($data['value'] ?? null) {
                            'published' => $query->whereNotNull('published_at')->where('published_at', '<=', now()),
                            'draft' => $query->whereNull('published_at'),
                            'scheduled' => $query->whereNotNull('published_at')->where('published_at', '>', now()),
                            default => $query,
                        };
                    }),

                \Filament\Tables\Filters\TernaryFilter::make('featured')
                    ->label('Featured Posts'),
            ])
            ->actions([
                \Filament\Tables\Actions\ViewAction::make()
                    ->url(fn ($record) => $record->url)
                    ->openUrlInNewTab(),

                EditAction::make(),

                \Filament\Tables\Actions\Action::make('duplicate')
                    ->label('Duplicate')
                    ->icon('heroicon-o-document-duplicate')
                    ->action(function ($record) {
                        $newPost = $record->replicate();
                        $newPost->title = $record->title . ' (Copy)';
                        $newPost->slug = $record->slug . '-copy';
                        $newPost->published_at = null;
                        $newPost->save();

                        return redirect()->route('filament.admin.resources.posts.edit', $newPost);
                    })
                    ->requiresConfirmation(),

                DeleteAction::make()->after(
                    function ($record) {
                        cache()->flush();
                    }
                ),
            ])
            ->bulkActions([
                \Filament\Tables\Actions\BulkActionGroup::make([
                    \Filament\Tables\Actions\DeleteBulkAction::make(),

                    \Filament\Tables\Actions\BulkAction::make('publish')
                        ->label('Publish Selected')
                        ->icon('heroicon-o-check')
                        ->action(function ($records) {
                            $records->each(function ($record) {
                                $record->update(['published_at' => now()]);
                            });
                        })
                        ->requiresConfirmation()
                        ->color('success'),

                    \Filament\Tables\Actions\BulkAction::make('unpublish')
                        ->label('Unpublish Selected')
                        ->icon('heroicon-o-x-mark')
                        ->action(function ($records) {
                            $records->each(function ($record) {
                                $record->update(['published_at' => null]);
                            });
                        })
                        ->requiresConfirmation()
                        ->color('warning'),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPosts::route('/'),
            'create' => Pages\CreatePost::route('/create'),
            'edit' => Pages\EditPost::route('/{record}/edit'),
        ];
    }
}
