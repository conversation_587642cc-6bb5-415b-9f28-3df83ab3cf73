<?php

namespace App\Http\Controllers;

use App\Models\Post;
use Illuminate\Http\Request;
use Artesaos\SEOTools\Facades\SEOTools;
use Artesaos\SEOTools\Facades\OpenGraph;
use Artesaos\SEOTools\Facades\TwitterCard;
use Artesaos\SEOTools\Facades\JsonLd;

/**
 * Blog Controller with Multi-language and SEO Support
 * 
 * Handles blog listing and individual post display with:
 * - Multi-language content filtering
 * - Advanced SEO optimization
 * - Structured data implementation
 * - Social media optimization
 */
class BlogController extends Controller
{
    /**
     * Display blog listing page
     * 
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $locale = app()->getLocale();
        $perPage = 12;
        
        // Get posts for current language
        $posts = Post::forLocale($locale)
                    ->published()
                    ->with(['views']) // Eager load view counts
                    ->latest('published_at')
                    ->paginate($perPage);

        // Get featured posts
        $featuredPosts = Post::forLocale($locale)
                            ->published()
                            ->featured()
                            ->latest('published_at')
                            ->limit(3)
                            ->get();

        // SEO optimization
        $title = $locale === 'ar' 
            ? 'مدونة محول الصور - نصائح وإرشادات تحويل الصور'
            : 'Image Converter Blog - Tips and Guides for Image Conversion';
            
        $description = $locale === 'ar'
            ? 'اكتشف أحدث النصائح والإرشادات حول تحويل الصور وتحسينها. مقالات متخصصة في تنسيقات الصور والتحويل الرقمي.'
            : 'Discover the latest tips and guides on image conversion and optimization. Expert articles on image formats and digital conversion.';

        SEOTools::setTitle($title);
        SEOTools::setDescription($description);
        SEOTools::setCanonical(url()->current());

        // Open Graph
        OpenGraph::setTitle($title)
            ->setDescription($description)
            ->setUrl(url()->current())
            ->setType('website')
            ->addImage(asset('images/blog-og-image.png'));

        // Twitter Card
        TwitterCard::setType('summary_large_image')
            ->setTitle($title)
            ->setDescription($description)
            ->setImage(asset('images/blog-twitter-image.png'));

        // JSON-LD for Blog
        JsonLd::setTitle($title)
            ->setDescription($description)
            ->setType('Blog')
            ->addValue('url', url()->current())
            ->addValue('inLanguage', $locale)
            ->addValue('author', [
                '@type' => 'Organization',
                'name' => config('app.name')
            ]);

        return view('blog.index', compact('posts', 'featuredPosts', 'locale'));
    }

    /**
     * Display individual blog post
     * 
     * @param string $slug
     * @return \Illuminate\View\View
     */
    public function show(string $slug)
    {
        $locale = app()->getLocale();
        
        // Find post by slug and locale
        $post = Post::where('slug', $slug)
                   ->forLocale($locale)
                   ->published()
                   ->firstOrFail();

        // Record view
        views($post)->record();

        // Get related posts
        $relatedPosts = $post->getRelatedPosts(4);

        // SEO optimization
        SEOTools::setTitle($post->seo_title);
        SEOTools::setDescription($post->metaDescription ?: $post->excerpt);
        
        if ($post->metaKeywords) {
            SEOTools::metatags()->addKeyword(explode(',', $post->metaKeywords));
        }
        
        SEOTools::setCanonical($post->url);

        // Open Graph
        OpenGraph::setTitle($post->seo_title)
            ->setDescription($post->metaDescription ?: $post->excerpt)
            ->setUrl($post->url)
            ->setType('article')
            ->addProperty('article:published_time', $post->published_at->toISOString())
            ->addProperty('article:modified_time', $post->updated_at->toISOString())
            ->addProperty('article:author', $post->author_name ?: config('app.name'));

        if ($post->thumbnail) {
            OpenGraph::addImage(asset('storage/' . $post->thumbnail), [
                'height' => 630,
                'width' => 1200,
                'alt' => $post->title
            ]);
        }

        if ($post->tags) {
            foreach ($post->tags as $tag) {
                OpenGraph::addProperty('article:tag', $tag);
            }
        }

        // Twitter Card
        TwitterCard::setType('summary_large_image')
            ->setTitle($post->seo_title)
            ->setDescription($post->metaDescription ?: $post->excerpt);

        if ($post->thumbnail) {
            TwitterCard::setImage(asset('storage/' . $post->thumbnail));
        }

        // JSON-LD for Article
        $jsonLd = [
            '@context' => 'https://schema.org',
            '@type' => 'BlogPosting',
            'headline' => $post->title,
            'description' => $post->metaDescription ?: $post->excerpt,
            'image' => $post->thumbnail ? asset('storage/' . $post->thumbnail) : null,
            'author' => [
                '@type' => 'Person',
                'name' => $post->author_name ?: config('app.name')
            ],
            'publisher' => [
                '@type' => 'Organization',
                'name' => config('app.name'),
                'logo' => [
                    '@type' => 'ImageObject',
                    'url' => asset('images/logo.png')
                ]
            ],
            'datePublished' => $post->published_at->toISOString(),
            'dateModified' => $post->updated_at->toISOString(),
            'mainEntityOfPage' => [
                '@type' => 'WebPage',
                '@id' => $post->url
            ],
            'wordCount' => str_word_count(strip_tags($post->content)),
            'timeRequired' => 'PT' . $post->reading_time . 'M',
            'inLanguage' => $locale,
            'url' => $post->url
        ];

        if ($post->tags) {
            $jsonLd['keywords'] = implode(', ', $post->tags);
        }

        JsonLd::addValue('@context', 'https://schema.org');
        JsonLd::addValue('@type', 'BlogPosting');
        foreach ($jsonLd as $key => $value) {
            if ($value !== null) {
                JsonLd::addValue($key, $value);
            }
        }

        // Breadcrumb structured data
        JsonLd::addValue('breadcrumb', [
            '@type' => 'BreadcrumbList',
            'itemListElement' => [
                [
                    '@type' => 'ListItem',
                    'position' => 1,
                    'name' => __('Home'),
                    'item' => url('/')
                ],
                [
                    '@type' => 'ListItem',
                    'position' => 2,
                    'name' => __('Blog'),
                    'item' => localizedRoute('blog.index')
                ],
                [
                    '@type' => 'ListItem',
                    'position' => 3,
                    'name' => $post->title,
                    'item' => $post->url
                ]
            ]
        ]);

        return view('blog.show', compact('post', 'relatedPosts', 'locale'));
    }

    /**
     * Display posts by tag
     * 
     * @param string $tag
     * @return \Illuminate\View\View
     */
    public function tag(string $tag)
    {
        $locale = app()->getLocale();
        
        $posts = Post::forLocale($locale)
                    ->published()
                    ->whereJsonContains('tags', $tag)
                    ->latest('published_at')
                    ->paginate(12);

        $title = $locale === 'ar' 
            ? "مقالات بوسم: {$tag}"
            : "Posts tagged: {$tag}";

        SEOTools::setTitle($title);
        SEOTools::setDescription($title);

        return view('blog.tag', compact('posts', 'tag', 'locale'));
    }

    /**
     * Search posts
     * 
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function search(Request $request)
    {
        $locale = app()->getLocale();
        $query = $request->get('q', '');
        
        $posts = collect();
        
        if (strlen($query) >= 3) {
            $posts = Post::forLocale($locale)
                        ->published()
                        ->where(function ($q) use ($query) {
                            $q->where('title', 'LIKE', "%{$query}%")
                              ->orWhere('content', 'LIKE', "%{$query}%")
                              ->orWhere('metaDescription', 'LIKE', "%{$query}%");
                        })
                        ->latest('published_at')
                        ->paginate(12);
        }

        $title = $locale === 'ar' 
            ? "نتائج البحث: {$query}"
            : "Search results: {$query}";

        SEOTools::setTitle($title);
        SEOTools::setDescription($title);

        return view('blog.search', compact('posts', 'query', 'locale'));
    }
}
