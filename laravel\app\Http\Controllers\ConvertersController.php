<?php

namespace App\Http\Controllers;

use App\Models\ViewsCounter;
use App\Settings\ConvertersSlugsSettings;
use Artesaos\SEOTools\Facades\SEOTools;
use Artesaos\SEOTools\Facades\OpenGraph;
use Artesaos\SEOTools\Facades\TwitterCard;
use Artesaos\SEOTools\Facades\JsonLd;

class ConvertersController extends Controller
{
    /**
     * Display a specific converter page with enhanced SEO optimization
     *
     * @param string $slug The converter slug
     * @return \Illuminate\View\View
     */
    public static function index(string $slug)
    {
        $slugs = app(ConvertersSlugsSettings::class);

        // Resolve converter from slug (e.g. imageToAiSlug)
        $name = $slugs
            ->originalValues
            ->filter(fn ($value) => trim(strtolower($slug)) === $value)
            ->flip()
            ->first();

        $converter = collect(converters())->filter(
            fn ($converter) => $name === $converter['name']
        )->first();

        abort_if($converter === null, 404);

        $settings = app($converter['settings']);
        abort_if(!$settings->enabled, 404);

        // Enhanced SEO meta tags
        SEOTools::webPage(
            title: $settings->title,
            description: $settings->metaDescription,
            keyWords: $settings->metaKeywords ?? "image converter, {$converter['format']}, convert to {$converter['format']}, online image converter"
        );

        // Open Graph tags for social media sharing
        OpenGraph::setTitle($settings->title)
            ->setDescription($settings->metaDescription)
            ->setUrl(url()->current())
            ->setType('website')
            ->setSiteName(config('app.name'))
            ->addImage(asset('storage/logo.png'), [
                'height' => 630,
                'width' => 1200,
                'alt' => $settings->title
            ]);

        // Twitter Card optimization
        TwitterCard::setType('summary_large_image')
            ->setTitle($settings->title)
            ->setDescription($settings->metaDescription)
            ->setImage(asset('storage/logo.png'));

        // JSON-LD structured data for converter tool
        JsonLd::setTitle($settings->title)
            ->setDescription($settings->metaDescription)
            ->setType('SoftwareApplication')
            ->addValue('applicationCategory', 'UtilityApplication')
            ->addValue('operatingSystem', 'Web Browser')
            ->addValue('url', url()->current())
            ->addValue('offers', [
                '@type' => 'Offer',
                'price' => '0',
                'priceCurrency' => 'USD'
            ])
            ->addValue('featureList', [
                "Convert images to {$converter['format']} format",
                'Free online conversion',
                'No registration required',
                'Batch processing support'
            ]);

        // Add breadcrumb structured data
        JsonLd::addValue('breadcrumb', [
            '@type' => 'BreadcrumbList',
            'itemListElement' => [
                [
                    '@type' => 'ListItem',
                    'position' => 1,
                    'name' => __('Home'),
                    'item' => url('/')
                ],
                [
                    '@type' => 'ListItem',
                    'position' => 2,
                    'name' => $converter['entryTitle'],
                    'item' => url()->current()
                ]
            ]
        ]);

        // Track page views for analytics
        ViewsCounter::addPage(
            identifier: $settings->name,
            title: $converter['admin']['title']
        );

        return view('pages.converter', [
            'slug'             => $slug,
            'slugs'            => $slugs,
            'format'           => $converter['format'],
            'headerTitle'      => $settings->headerTitle,
            'headerSubtitle'   => $settings->headerSubtitle,
            'description'      => $settings->description,
            'showTopAd'        => $settings->showTopAd,
            'showBottomAd'     => $settings->showBottomAd,
            'showMiddleAd'     => $settings->showMiddleAd,
            'showShareButtons' => $settings->showShareButtons,
            'converter'        => $converter, // Add converter data for enhanced features
        ]);
    }
}
