<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('posts', function (Blueprint $table) {
            // Language and SEO fields
            $table->string('locale', 5)->default('en')->after('id');
            $table->string('meta_title')->nullable()->after('title');
            $table->text('excerpt')->nullable()->after('content');
            
            // Content enhancement fields
            $table->boolean('featured')->default(false)->after('published_at');
            $table->integer('reading_time')->nullable()->after('featured');
            $table->string('author_name')->nullable()->after('reading_time');
            
            // JSON fields for tags and related content
            $table->json('tags')->nullable()->after('author_name');
            $table->json('related_posts')->nullable()->after('tags');
            
            // Add indexes for better performance
            $table->index('locale');
            $table->index('featured');
            $table->index(['locale', 'published_at']);
            $table->index(['locale', 'featured']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('posts', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['locale', 'featured']);
            $table->dropIndex(['locale', 'published_at']);
            $table->dropIndex(['featured']);
            $table->dropIndex(['locale']);
            
            // Drop columns
            $table->dropColumn([
                'locale',
                'meta_title',
                'excerpt',
                'featured',
                'reading_time',
                'author_name',
                'tags',
                'related_posts'
            ]);
        });
    }
};
