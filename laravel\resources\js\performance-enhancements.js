/**
 * Performance Enhancements and User Experience Improvements
 * for Image Converter Application
 */

// Performance monitoring and optimization
class PerformanceMonitor {
    constructor() {
        this.metrics = {};
        this.init();
    }

    init() {
        // Monitor Core Web Vitals
        this.observeWebVitals();
        
        // Optimize images loading
        this.optimizeImageLoading();
        
        // Implement service worker for caching
        this.registerServiceWorker();
        
        // Add keyboard navigation
        this.enhanceKeyboardNavigation();
        
        // Optimize scroll performance
        this.optimizeScrolling();
    }

    observeWebVitals() {
        // Largest Contentful Paint (LCP)
        new PerformanceObserver((entryList) => {
            for (const entry of entryList.getEntries()) {
                this.metrics.lcp = entry.startTime;
                console.log('LCP:', entry.startTime);
            }
        }).observe({ entryTypes: ['largest-contentful-paint'] });

        // First Input Delay (FID)
        new PerformanceObserver((entryList) => {
            for (const entry of entryList.getEntries()) {
                this.metrics.fid = entry.processingStart - entry.startTime;
                console.log('FID:', this.metrics.fid);
            }
        }).observe({ entryTypes: ['first-input'] });

        // Cumulative Layout Shift (CLS)
        let clsValue = 0;
        new PerformanceObserver((entryList) => {
            for (const entry of entryList.getEntries()) {
                if (!entry.hadRecentInput) {
                    clsValue += entry.value;
                }
            }
            this.metrics.cls = clsValue;
            console.log('CLS:', clsValue);
        }).observe({ entryTypes: ['layout-shift'] });
    }

    optimizeImageLoading() {
        // Lazy loading for images
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        observer.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('SW registered:', registration);
                })
                .catch(error => {
                    console.log('SW registration failed:', error);
                });
        }
    }

    enhanceKeyboardNavigation() {
        // Add keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K for search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.querySelector('[data-search]');
                if (searchInput) {
                    searchInput.focus();
                }
            }

            // Escape to close modals
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal:target');
                if (openModal) {
                    window.location.hash = '';
                }
            }
        });

        // Improve focus management
        this.manageFocus();
    }

    manageFocus() {
        // Skip links for accessibility
        const skipLink = document.querySelector('[href="#main-content"]');
        if (skipLink) {
            skipLink.addEventListener('click', (e) => {
                e.preventDefault();
                const mainContent = document.getElementById('main-content');
                if (mainContent) {
                    mainContent.focus();
                    mainContent.scrollIntoView({ behavior: 'smooth' });
                }
            });
        }

        // Focus trap for modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                const modal = document.querySelector('.modal[open]');
                if (modal) {
                    this.trapFocus(e, modal);
                }
            }
        });
    }

    trapFocus(e, container) {
        const focusableElements = container.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (e.shiftKey && document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
        } else if (!e.shiftKey && document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
        }
    }

    optimizeScrolling() {
        // Throttle scroll events
        let ticking = false;
        
        const updateScrollPosition = () => {
            // Update scroll-based animations
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            
            // Parallax effect for hero section
            const hero = document.querySelector('.hero');
            if (hero) {
                hero.style.transform = `translateY(${rate}px)`;
            }
            
            ticking = false;
        };

        document.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollPosition);
                ticking = true;
            }
        });
    }
}

// Enhanced FilePond integration
class FilePondEnhancer {
    constructor() {
        this.init();
    }

    init() {
        // Wait for FilePond to be available
        if (typeof FilePond !== 'undefined') {
            this.enhanceFilePond();
        } else {
            // Retry after a short delay
            setTimeout(() => this.init(), 100);
        }
    }

    enhanceFilePond() {
        // Add progress tracking
        FilePond.setOptions({
            onprocessfilestart: (file) => {
                console.log('Processing started:', file.filename);
                this.showProcessingIndicator();
            },
            onprocessfile: (error, file) => {
                if (error) {
                    console.error('Processing error:', error);
                    this.showError(error.body);
                } else {
                    console.log('Processing completed:', file.filename);
                }
                this.hideProcessingIndicator();
            },
            onprocessfiles: () => {
                console.log('All files processed');
                this.enableConvertButton();
            }
        });
    }

    showProcessingIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'processing-indicator';
        indicator.innerHTML = `
            <div class="flex items-center justify-center p-4">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span class="ml-2">Processing...</span>
            </div>
        `;
        document.body.appendChild(indicator);
    }

    hideProcessingIndicator() {
        const indicator = document.querySelector('.processing-indicator');
        if (indicator) {
            indicator.remove();
        }
    }

    showError(message) {
        const toast = document.createElement('div');
        toast.className = 'fixed top-4 right-4 bg-red-500 text-white p-4 rounded-lg shadow-lg z-50';
        toast.textContent = message;
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 5000);
    }

    enableConvertButton() {
        const convertBtn = document.querySelector('[data-convert]');
        if (convertBtn) {
            convertBtn.disabled = false;
            convertBtn.classList.remove('opacity-50');
        }
    }
}

// RTL Support Enhancement
class RTLSupport {
    constructor() {
        this.init();
    }

    init() {
        const isRTL = document.documentElement.dir === 'rtl';
        if (isRTL) {
            this.enhanceRTLExperience();
        }
    }

    enhanceRTLExperience() {
        // Adjust animations for RTL
        const cards = document.querySelectorAll('.card');
        cards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'scale(1.05) rotateY(5deg)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'scale(1) rotateY(0deg)';
            });
        });

        // Adjust scroll behavior
        this.adjustScrollBehavior();
    }

    adjustScrollBehavior() {
        // Custom scroll behavior for RTL
        const smoothScrollElements = document.querySelectorAll('[data-smooth-scroll]');
        smoothScrollElements.forEach(element => {
            element.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(element.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }
}

// Initialize all enhancements when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new PerformanceMonitor();
    new FilePondEnhancer();
    new RTLSupport();
    
    // Add loading states
    document.body.classList.add('loaded');
});

// Export for use in other modules
window.ImageConverterEnhancements = {
    PerformanceMonitor,
    FilePondEnhancer,
    RTLSupport
};
